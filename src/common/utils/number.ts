/**
 * Round a number to a specified number of decimal places
 * @param number - The number to round
 * @param decimalPlaces - The number of decimal places to round to (default is 2)
 * @returns The rounded number
 */
export const roundDecimal = (number: number, decimalPlaces: number = 2) => {
  return (
    Math.round(number * Math.pow(10, decimalPlaces)) /
    Math.pow(10, decimalPlaces)
  );
};

/**
 * Calculate the percentage change between two numbers
 * @param current - The current number
 * @param previous - The previous number
 * @returns The percentage change
 */
export const calculatePercentageChange = (
  current: number,
  previous: number,
) => {
  if (previous === 0) {
    if (current === 0) {
      return 0;
    }
    return 100;
  }
  return ((current - previous) / previous) * 100;
};
