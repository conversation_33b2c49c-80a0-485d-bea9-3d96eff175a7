export const parseJsonFromMarkdown = (jsonString: string) => {
  try {
    return JSON.parse(
      jsonString.replace('```json', '').replace('```', '').replaceAll('\n', ''),
    );
  } catch (error) {
    console.error('Error parsing JSON:', error);
    throw new Error('Error parsing <PERSON>SO<PERSON> from Markdown');
  }
};

export const parseJsonFromTextAIResponse = <T = any>(text: string): T => {
  try {
    return JSON.parse(text.replace(/```json\n/, '').replace(/\n```/, '')) as T;
  } catch (error) {
    console.error('Error parsing JSON:', error);
    throw new Error('Error parsing <PERSON><PERSON><PERSON> from Text');
  }
};
