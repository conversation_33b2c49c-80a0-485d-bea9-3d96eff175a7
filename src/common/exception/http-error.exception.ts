import { ErrorCode } from '@app/common/types';
import {
  HttpException,
  HttpExceptionOptions,
} from '@nestjs/common/exceptions/http.exception';

export class HttpErrorException extends HttpException {
  public readonly errorCode: string | number;
  constructor(error: ErrorCode, options?: HttpExceptionOptions) {
    super(error.message, error.statusCode, options);
    this.errorCode = error.code;
  }
}
