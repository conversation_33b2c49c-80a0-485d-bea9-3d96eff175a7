import { ApiProperty } from '@nestjs/swagger';

export class SuccessResponseDto<T = any> {
  @ApiProperty()
  public readonly data?: T;

  @ApiProperty()
  success: boolean;

  constructor(data?: T) {
    this.data = data;
    this.success = true;
  }
}

export type PaginationPayload = {
  page: number;
  limit: number;
  total: number;
};

export class Pagination {
  @ApiProperty()
  public readonly total: number;
  @ApiProperty()
  public readonly page: number;
  @ApiProperty()
  public readonly limit: number;

  protected constructor(page: number, limit: number, total: number) {
    this.page = page;
    this.limit = limit;
    this.total = total;
  }

  public static create(payload: PaginationPayload): Pagination {
    return new Pagination(payload.page, payload.limit, payload.total);
  }
}

export type PagedResultDtoPayload<TDto> = {
  pagination: Pagination;
  data: TDto[];
};

export class PagedResultDto<TDto> {
  @ApiProperty({ type: () => Pagination })
  public readonly pagination: Pagination;
  @ApiProperty({ isArray: true })
  public readonly data: TDto[];
  constructor(pagination: Pagination, data: TDto[]) {
    this.pagination = pagination;
    this.data = data;
  }

  public static create<TDto>(
    payload: PagedResultDtoPayload<TDto>,
  ): PagedResultDto<TDto> {
    return new PagedResultDto(payload.pagination, payload.data);
  }
}

export class SuccessResponsePagedDto<TDto> {
  @ApiProperty({ type: () => Pagination })
  public readonly pagination: Pagination;
  @ApiProperty({ isArray: true })
  public readonly data: TDto[];
  @ApiProperty()
  public readonly success: boolean;
  constructor(pagination: Pagination, data: TDto[]) {
    this.pagination = pagination;
    this.data = data;
    this.success = true;
  }

  public static create<TDto>(
    payload: PagedResultDtoPayload<TDto>,
  ): SuccessResponsePagedDto<TDto> {
    return new SuccessResponsePagedDto(payload.pagination, payload.data);
  }
}
