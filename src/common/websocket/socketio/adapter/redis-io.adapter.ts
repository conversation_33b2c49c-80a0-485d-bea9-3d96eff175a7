import { INestApplicationContext, Logger } from '@nestjs/common';
import { IoAdapter } from '@nestjs/platform-socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { createClient, RedisClientOptions } from 'redis';
import { ServerOptions } from 'socket.io';

export class RedisIoAdapter extends IoAdapter {
  private readonly logger = new Logger(RedisIoAdapter.name);
  private adapterConstructor: ReturnType<typeof createAdapter>;

  constructor(
    private app: INestApplicationContext,
    private redisOptions: RedisClientOptions,
  ) {
    super(app);
  }

  async connectToRedis(): Promise<void> {
    try {
      const pubClient = createClient({
        ...this.redisOptions,
        socket: {
          ...(this.redisOptions.socket || {}),
          reconnectStrategy: (retries) => Math.min(retries * 50, 1000),
        },
      });

      const subClient = pubClient.duplicate();

      // Handle Redis connection errors
      pubClient.on('error', (err) => {
        this.logger.error(`Redis Pub Client Error: ${err.message}`, err.stack);
      });

      subClient.on('error', (err) => {
        this.logger.error(`Redis Sub Client Error: ${err.message}`, err.stack);
      });

      // Setup reconnection handlers
      pubClient.on('reconnecting', () => {
        this.logger.warn('Redis Pub Client reconnecting...');
      });

      subClient.on('reconnecting', () => {
        this.logger.warn('Redis Sub Client reconnecting...');
      });

      // Connect to Redis
      await Promise.all([pubClient.connect(), subClient.connect()]);

      // Test the connection
      await pubClient.ping();
      this.logger.log('Redis connection established successfully');

      // Create the adapter
      this.adapterConstructor = createAdapter(pubClient, subClient);
      this.logger.log('Redis adapter has been configured successfully');
    } catch (error) {
      this.logger.error(
        `Failed to connect to Redis: ${error.message}`,
        error.stack,
      );

      // Fallback to in-memory adapter if Redis is not available
      this.logger.warn('Using in-memory adapter as fallback');
      throw error;
    }
  }

  createIOServer(port: number, options?: ServerOptions): any {
    const server = super.createIOServer(port, options);

    if (this.adapterConstructor) {
      server.adapter(this.adapterConstructor);
      this.logger.log('Socket.IO server is now using Redis adapter');
    } else {
      this.logger.warn('Socket.IO server is using default in-memory adapter');
    }

    return server;
  }
}
