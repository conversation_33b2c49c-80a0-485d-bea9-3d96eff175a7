import validateConfig from '@common/config/validate-config';
import { registerAs } from '@nestjs/config';
import { IsPort, IsString } from 'class-validator';

export type RedisConfigType = {
  host: string;
  port: number;
};

class EnvironmentVariablesValidator {
  @IsString()
  REDIS_HOST: string;

  @IsPort()
  REDIS_PORT: string;
}

export const redisConfig = registerAs<RedisConfigType>('redis', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
  };
});
