import { redisConfig } from '@app/config/redis.config';
import { AlertConfigType } from '@app/modules/alert/alert.config';
import { AlertModule } from '@app/modules/alert/alert.module';
import { AuthModule } from '@app/modules/auth/auth.module';
import { DatabaseModule } from '@app/modules/database/database.module';
import { MailerModule } from '@app/modules/mailer/mailer.module';
import { PasskeyModule } from '@app/modules/passkey/passkey.module';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CacheStoreModule } from '@app/modules/cache-store/cache-store.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [redisConfig],
    }),
    CacheStoreModule,
    DatabaseModule,
    AuthModule,
    MailerModule,
    AlertModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (
        configService: ConfigService<{ alert: AlertConfigType }>,
      ) => {
        const config = configService.get('alert', { infer: true });
        return {
          email: config?.email || { enabled: false },
          slack: config?.slack || { enabled: false, channel: '', token: '' },
        };
      },
      inject: [ConfigService],
    }),
    PasskeyModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
