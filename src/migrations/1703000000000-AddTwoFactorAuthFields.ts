import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddTwoFactorAuthFields1703000000000 implements MigrationInterface {
  name = 'AddTwoFactorAuthFields1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add is_two_factor_enabled column
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'is_two_factor_enabled',
        type: 'boolean',
        default: false,
        isNullable: false,
      }),
    );

    // Add two_factor_secret column
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'two_factor_secret',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove two_factor_secret column
    await queryRunner.dropColumn('users', 'two_factor_secret');

    // Remove is_two_factor_enabled column
    await queryRunner.dropColumn('users', 'is_two_factor_enabled');
  }
}
