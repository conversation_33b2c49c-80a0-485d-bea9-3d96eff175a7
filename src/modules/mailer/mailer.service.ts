import { MailerConfigType } from '@app/modules/mailer/mailer.config';
import { MailerFactory } from '@app/modules/mailer/mailer/mailer.factory';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as handlebars from 'handlebars';
import * as path from 'path';

@Injectable()
export class MailerService {
  _templateDir: string | undefined;
  constructor(
    private readonly mailerFactory: MailerFactory,
    private readonly configService: ConfigService<{
      mailer: MailerConfigType;
    }>,
  ) {
    this._templateDir = this.configService.get('mailer.templateDir', {
      infer: true,
    });
  }

  sendMail(payload: {
    to: string | string[];
    subject: string;
    text?: string;
    html?: string;
    attachments?: { filename: string; content: Buffer }[];
  }) {
    const { to, subject, text, html, attachments } = payload;
    const from = this.configService.getOrThrow('mailer.from', {
      infer: true,
    });
    const replyTo = this.configService.getOrThrow('mailer.replyTo', {
      infer: true,
    });
    const bccEmail = this.configService.getOrThrow('mailer.bccEmail', {
      infer: true,
    });
    const bccName = this.configService.getOrThrow('mailer.bccName', {
      infer: true,
    });
    const replyToName = this.configService.getOrThrow('mailer.replyToName', {
      infer: true,
    });
    return this.mailerFactory.getMailerService().sendMail({
      to,
      subject,
      text,
      html,
      attachments,
      from,
      replyTo,
      bccEmail,
      bccName,
      replyToName,
    });
  }

  async sendMailWithTemplate(payload: {
    to: string | string[];
    template: string;
    subject: string;
    context?: Record<string, any>;
    attachments?: { filename: string; content: Buffer }[];
  }) {
    if (!this._templateDir) {
      throw new Error('Template directory is not set');
    }
    const { to, template, context, attachments, subject } = payload;
    const html = this.prepareTemplate(template, context);
    return this.sendMail({
      to,
      subject: subject,
      html,
      attachments,
    });
  }

  prepareTemplate(template: string, context?: Record<string, any>) {
    if (!this._templateDir) {
      throw new Error('Template directory is not set');
    }
    const templatePath = path.join(this._templateDir, `${template}.hbs`);
    const templateContent = fs.readFileSync(templatePath, 'utf8');
    const compiledTemplate = handlebars.compile(templateContent);
    return compiledTemplate(context || {});
  }
}
