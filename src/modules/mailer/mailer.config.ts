import { IsBoolean, IsIn, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';

import validateConfig from '@app/common/config/validate-config';
import { registerAs } from '@nestjs/config';
import { IsString } from 'class-validator';

export type MailerConfigType = {
  smtp?: {
    host: string;
    port: number;
    secure: boolean;
    user: string;
    pass: string;
    requireTLS: boolean;
  };
  from: string;
  transport?: string;
  bccEmail?: string;
  bccName?: string;
  replyTo?: string;
  replyToName?: string;
  mailer: 'smtp' | 'sendgrid' | 'mailgun';
  templateDir?: string;
  sendgrid?: {
    apiKey: string;
  };
  mailgun?: {
    apiKey: string;
    domain: string;
    baseUrl?: string;
  };
};

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  MAILER_SMTP_HOST: string;

  @IsNumber()
  @IsOptional()
  MAILER_SMTP_PORT: number;

  @IsString()
  @IsOptional()
  MAIL_SMTP_USER: string;

  @IsString()
  @IsOptional()
  MAILER_SMTP_PASS: string;

  @IsBoolean()
  @IsOptional()
  MAILER_SMTP_SECURE: boolean;

  @IsBoolean()
  @IsOptional()
  MAILER_SMTP_REQUIRE_TLS: boolean;

  @IsString()
  @IsOptional()
  MAILER_FROM: string;

  @IsString()
  @IsOptional()
  MAILER_REPLY_TO: string;

  @IsString()
  @IsIn(['smtp', 'sendgrid', 'mailgun'])
  @IsOptional()
  MAILER_TRANSPORT: string;

  @IsString()
  @IsOptional()
  MAILER_SENDGRID_API_KEY: string;

  @IsString()
  @IsOptional()
  MAILER_MAILGUN_API_KEY: string;

  @IsString()
  @IsOptional()
  MAILER_MAILGUN_DOMAIN: string;

  @IsString()
  @IsOptional()
  MAILER_MAILGUN_BASE_URL: string;
}

export default registerAs<MailerConfigType>('mailer', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    smtp: {
      host: process.env.MAILER_SMTP_HOST || '',
      port: parseInt(process.env.MAILER_SMTP_PORT || '587', 10),
      secure: process.env.MAILER_SMTP_SECURE === 'true',
      user: process.env.MAILER_SMTP_USER || '',
      pass: process.env.MAILER_SMTP_PASS || '',
      requireTLS: process.env.MAILER_SMTP_REQUIRE_TLS === 'true',
    },
    from: process.env.MAILER_FROM || '',
    mailer: process.env.MAILER_TRANSPORT as 'smtp' | 'sendgrid' | 'mailgun',
    bccEmail: process.env.MAILER_BCC_EMAIL || '',
    bccName: process.env.MAILER_BCC_NAME || '',
    replyTo: process.env.MAILER_REPLY_TO || '',
    templateDir: process.env.MAILER_TEMPLATE_DIR || '',
    sendgrid: {
      apiKey: process.env.MAILER_SENDGRID_API_KEY || '',
    },
    mailgun: {
      apiKey: process.env.MAILER_MAILGUN_API_KEY || '',
      domain: process.env.MAILER_MAILGUN_DOMAIN || '',
      baseUrl: process.env.MAILER_MAILGUN_BASE_URL || '',
    },
  };
});
