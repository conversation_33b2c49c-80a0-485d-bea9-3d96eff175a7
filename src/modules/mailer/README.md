# Mailer Module

A flexible, multi-provider email service for NestJS applications. This module supports multiple email providers with a unified interface, making it easy to switch between different email services without changing your application code.

## Supported Providers

- **SMTP** - Traditional SMTP server support
- **SendGrid** - Cloud-based email delivery service  
- **Mailgun** - Powerful email delivery platform

## Table of Contents

- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [Provider-Specific Setup](#provider-specific-setup)
- [Features](#features)
- [API Reference](#api-reference)
- [Examples](#examples)

## Installation

The mailer module is included in this boilerplate. Required dependencies are already installed:

```bash
# Core dependencies (already included)
npm install @nestjs/config nodemailer handlebars

# Provider-specific dependencies (install as needed)
npm install @sendgrid/mail      # For SendGrid
npm install mailgun.js form-data # For Mailgun
```

## Configuration

### Environment Variables

Set these environment variables in your `.env` file:

```bash
# Required: Choose your email provider
MAILER_TRANSPORT=smtp|sendgrid|mailgun

# Required: Email settings
MAILER_FROM="Your App <<EMAIL>>"

# Optional: Global settings
MAILER_BCC_EMAIL=<EMAIL>
MAILER_BCC_NAME="Admin Team"
MAILER_REPLY_TO=<EMAIL>
MAILER_REPLY_TO_NAME="Support Team"
MAILER_TEMPLATE_DIR=./templates

# Provider-specific settings (see sections below)
```

### Module Import

The mailer module is already configured in your application. To use it in your services:

```typescript
import { Injectable } from '@nestjs/common';
import { MailerService } from '@app/modules/mailer/mailer.service';

@Injectable()
export class YourService {
  constructor(private readonly mailerService: MailerService) {}
}
```

## Usage

### Basic Email

```typescript
const result = await this.mailerService.sendMail({
  to: '<EMAIL>',
  subject: 'Welcome!',
  html: '<h1>Welcome to our platform!</h1>',
  text: 'Welcome to our platform!', // Optional fallback
});

if (result.status === 'failed') {
  console.error('Email failed:', result.error);
}
```

### Multiple Recipients

```typescript
await this.mailerService.sendMail({
  to: ['<EMAIL>', '<EMAIL>'],
  subject: 'Team Update',
  html: '<p>Important team announcement...</p>',
});
```

### Email with Attachments

```typescript
await this.mailerService.sendMail({
  to: '<EMAIL>',
  subject: 'Your Report',
  html: '<p>Please find your report attached.</p>',
  attachments: [
    {
      filename: 'report.pdf',
      content: pdfBuffer, // Buffer containing file data
    },
    {
      filename: 'data.csv',
      content: csvBuffer,
    },
  ],
});
```

### Template-Based Emails

```typescript
// Requires MAILER_TEMPLATE_DIR to be set
await this.mailerService.sendMailWithTemplate({
  to: '<EMAIL>',
  template: 'welcome', // Looks for templates/welcome.hbs
  subject: 'Welcome to Our Platform!',
  context: {
    userName: 'John Doe',
    activationLink: 'https://yourapp.com/activate/123',
  },
});
```

## Provider-Specific Setup

### SMTP Configuration

For traditional SMTP servers (Gmail, Office365, custom SMTP):

```bash
MAILER_TRANSPORT=smtp
MAILER_SMTP_HOST=smtp.gmail.com
MAILER_SMTP_PORT=587
MAILER_SMTP_SECURE=false
MAILER_SMTP_REQUIRE_TLS=true
MAILER_SMTP_USER=<EMAIL>
MAILER_SMTP_PASS=your-app-password
```

**Gmail Setup:**
1. Enable 2-factor authentication
2. Generate an app password
3. Use the app password as `MAILER_SMTP_PASS`

### SendGrid Configuration

For SendGrid cloud email service:

```bash
MAILER_TRANSPORT=sendgrid
MAILER_SENDGRID_API_KEY=your-sendgrid-api-key
```

**SendGrid Setup:**
1. Create a SendGrid account
2. Generate an API key with mail send permissions
3. Verify your sender domain/email

### Mailgun Configuration

For Mailgun email delivery platform:

```bash
MAILER_TRANSPORT=mailgun
MAILER_MAILGUN_API_KEY=your-mailgun-api-key
MAILER_MAILGUN_DOMAIN=yourdomain.com
MAILER_MAILGUN_BASE_URL=https://api.mailgun.net  # Optional, use https://api.eu.mailgun.net for EU
```

**Mailgun Setup:**
1. Create a Mailgun account
2. Add and verify your domain
3. Get your API key from the dashboard
4. Use EU endpoint if your account is in EU region

## Features

### ✅ Supported Features

| Feature | SMTP | SendGrid | Mailgun |
|---------|------|----------|---------|
| HTML Emails | ✅ | ✅ | ✅ |
| Text Emails | ✅ | ✅ | ✅ |
| Multiple Recipients | ✅ | ✅ | ✅ |
| Attachments | ✅ | ✅ | ✅ |
| Reply-To Headers | ✅ | ✅ | ✅ |
| BCC Recipients | ✅ | ✅ | ✅ |
| Template Support | ✅ | ✅ | ✅ |
| Error Handling | ✅ | ✅ | ✅ |

### Template System

Uses Handlebars templating engine. Create `.hbs` files in your template directory:

**templates/welcome.hbs:**
```handlebars
<!DOCTYPE html>
<html>
<head>
    <title>Welcome</title>
</head>
<body>
    <h1>Hello {{userName}}!</h1>
    <p>Welcome to our platform. Click <a href="{{activationLink}}">here</a> to activate your account.</p>
</body>
</html>
```

## API Reference

### MailerService.sendMail(payload)

Send a simple email.

**Parameters:**
- `payload.to` - Recipient email(s) (string or string[])
- `payload.subject` - Email subject
- `payload.html` - HTML content (optional)
- `payload.text` - Plain text content (optional)
- `payload.attachments` - File attachments (optional)

**Returns:** `Promise<MailSendResult>`

### MailerService.sendMailWithTemplate(payload)

Send an email using a Handlebars template.

**Parameters:**
- `payload.to` - Recipient email(s)
- `payload.template` - Template name (without .hbs extension)
- `payload.subject` - Email subject
- `payload.context` - Template variables (optional)
- `payload.attachments` - File attachments (optional)

**Returns:** `Promise<MailSendResult>`

### MailSendResult

```typescript
type MailSendResult = {
  status: 'sent' | 'failed';
  error?: string;
};
```

## Examples

### Welcome Email Service

```typescript
import { Injectable } from '@nestjs/common';
import { MailerService } from '@app/modules/mailer/mailer.service';

@Injectable()
export class WelcomeEmailService {
  constructor(private readonly mailerService: MailerService) {}

  async sendWelcomeEmail(user: { email: string; name: string }) {
    const result = await this.mailerService.sendMailWithTemplate({
      to: user.email,
      template: 'welcome',
      subject: 'Welcome to Our Platform!',
      context: {
        userName: user.name,
        year: new Date().getFullYear(),
      },
    });

    if (result.status === 'failed') {
      throw new Error(`Failed to send welcome email: ${result.error}`);
    }

    return result;
  }

  async sendPasswordReset(email: string, resetToken: string) {
    const resetLink = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    
    return this.mailerService.sendMail({
      to: email,
      subject: 'Password Reset Request',
      html: `
        <h2>Password Reset</h2>
        <p>Click the link below to reset your password:</p>
        <a href="${resetLink}">Reset Password</a>
        <p>This link expires in 1 hour.</p>
      `,
    });
  }
}
```

### Notification Service

```typescript
@Injectable()
export class NotificationService {
  constructor(private readonly mailerService: MailerService) {}

  async sendInvoice(customerEmail: string, invoicePdf: Buffer) {
    return this.mailerService.sendMail({
      to: customerEmail,
      subject: 'Your Invoice',
      html: '<p>Thank you for your purchase. Your invoice is attached.</p>',
      attachments: [
        {
          filename: 'invoice.pdf',
          content: invoicePdf,
        },
      ],
    });
  }

  async sendBulkNewsletter(subscribers: string[], content: string) {
    // Send in batches to avoid rate limits
    const batchSize = 100;
    const results = [];

    for (let i = 0; i < subscribers.length; i += batchSize) {
      const batch = subscribers.slice(i, i + batchSize);
      
      const result = await this.mailerService.sendMail({
        to: batch,
        subject: 'Monthly Newsletter',
        html: content,
      });
      
      results.push(result);
      
      // Wait between batches
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    return results;
  }
}
```

## Best Practices

### 1. Error Handling
Always check the result status and handle failures appropriately:

```typescript
const result = await this.mailerService.sendMail(payload);
if (result.status === 'failed') {
  // Log error, retry, or notify administrators
  this.logger.error('Email failed', result.error);
}
```

### 2. Template Organization
Organize templates by purpose:
```
templates/
├── auth/
│   ├── welcome.hbs
│   ├── password-reset.hbs
│   └── email-verification.hbs
├── notifications/
│   ├── order-confirmation.hbs
│   └── shipping-update.hbs
└── marketing/
    └── newsletter.hbs
```

### 3. Rate Limiting
Be mindful of provider rate limits:
- **SMTP**: Depends on your server
- **SendGrid**: 100 emails/second by default
- **Mailgun**: 10,000 emails/hour by default

### 4. Environment-Specific Configuration
Use different providers for different environments:
- **Development**: SMTP (local testing)
- **Staging**: SendGrid (cost-effective)
- **Production**: Mailgun (high deliverability)

## Troubleshooting

### Common Issues

**1. SMTP Authentication Failed**
- Verify credentials
- Check if 2FA is enabled (use app passwords)
- Ensure SMTP settings match your provider

**2. SendGrid/Mailgun API Errors**
- Verify API key is correct and active
- Check sender domain verification
- Review account sending limits

**3. Template Not Found**
- Verify `MAILER_TEMPLATE_DIR` path
- Ensure template file exists with `.hbs` extension
- Check file permissions

**4. Attachments Too Large**
- Most providers limit attachment size (25MB typical)
- Consider using cloud storage links for large files

### Debug Mode

Enable debug logging by setting log level in your application:

```typescript
// In main.ts or your logger configuration
const app = await NestFactory.create(AppModule, {
  logger: ['error', 'warn', 'log', 'debug', 'verbose'],
});
```

## Security Considerations

1. **API Keys**: Store in environment variables, never in code
2. **SMTP Credentials**: Use app passwords, not account passwords
3. **Template Injection**: Sanitize user input in template contexts
4. **Rate Limiting**: Implement application-level rate limiting for user-triggered emails
5. **Validation**: Always validate email addresses before sending

---

For more information about specific providers:
- [SendGrid Documentation](https://docs.sendgrid.com/)
- [Mailgun Documentation](https://documentation.mailgun.com/)
- [Nodemailer Documentation](https://nodemailer.com/) 