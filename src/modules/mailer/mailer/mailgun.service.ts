import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as formData from 'form-data';
import Mailgun from 'mailgun.js';
import {
  IMailerService,
  MailerPayload,
  MailSendResult,
} from './mailer.interface';
import { MailerConfigType } from '@app/modules/mailer/mailer.config';

@Injectable()
export class MailgunMailerService implements IMailerService {
  private readonly logger = new Logger(MailgunMailerService.name);
  private readonly mailgun: any;
  private readonly domain: string;

  constructor(
    private readonly configService: ConfigService<{
      mailer: MailerConfigType;
    }>,
  ) {
    const mailgunConfig = this.configService.get('mailer.mailgun', {
      infer: true,
    });
    const apiKey = mailgunConfig?.apiKey;
    const domain = mailgunConfig?.domain;
    const baseUrl = mailgunConfig?.baseUrl || 'https://api.mailgun.net';

    if (!apiKey || !domain) {
      throw new Error('Mailgun API key and domain are required');
    }
    this.domain = domain;

    const mailgunClient = new Mailgun(formData);
    this.mailgun = mailgunClient.client({
      username: 'api',
      key: apiKey,
      url: baseUrl,
    });
  }

  async sendMail(payload: MailerPayload): Promise<MailSendResult> {
    try {
      const {
        to,
        from,
        replyTo,
        replyToName,
        bccEmail,
        bccName,
        subject,
        text,
        html,
        attachments,
      } = payload;

      // Prepare the email data
      const emailData: any = {
        from,
        to: Array.isArray(to) ? to.join(',') : to,
        subject,
        text,
        html,
      };

      // Add reply-to if specified
      if (replyTo) {
        emailData['h:Reply-To'] =
          replyToName && replyTo ? `${replyToName} <${replyTo}>` : replyTo;
      }

      // Add BCC if specified
      if (bccEmail) {
        emailData.bcc =
          bccName && bccEmail ? `${bccName} <${bccEmail}>` : bccEmail;
      }

      // Add attachments if specified
      if (attachments && attachments.length > 0) {
        emailData.attachment = attachments.map((attachment) => ({
          filename: attachment.filename,
          data: attachment.content,
        }));
      }

      const response = await this.mailgun.messages.create(
        this.domain,
        emailData,
      );

      this.logger.log(`Email sent successfully: ${response.id}`);

      return {
        status: 'sent',
      };
    } catch (error) {
      this.logger.error('Failed to send email via Mailgun', error);
      return {
        status: 'failed',
        error: error.message,
      };
    }
  }
}
