import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as sgMail from '@sendgrid/mail';
import {
  IMailerService,
  MailerPayload,
  MailSendResult,
} from './mailer.interface';

@Injectable()
export class SendgridMailerService implements IMailerService {
  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get('mail.sendgrid.apiKey');
    sgMail.setApiKey(apiKey);
  }

  private readonly logger = new Logger(SendgridMailerService.name);

  async sendMail(payload: MailerPayload): Promise<MailSendResult> {
    try {
      const {
        to,
        from,
        replyTo,
        replyToName,
        bccEmail,
        bccName,
        subject,
        text,
        html,
        attachments,
      } = payload;
      const msg: sgMail.MailDataRequired = {
        to,
        from,
        replyTo:
          replyToName && replyTo
            ? {
                name: replyToName,
                email: replyTo,
              }
            : replyTo,
        bcc:
          bccEmail && bccName
            ? {
                email: bccEmail,
                name: bccName,
              }
            : bccEmail,
        subject,
        text,
        html,
        content: [{ type: 'text/html', value: html || '' }],
        attachments:
          attachments?.map((attachment) => ({
            content: attachment.content.toString('base64'),
            filename: attachment.filename,
          })) || undefined,
      };
      const response = await sgMail.send(msg);
      console.log(response);
      return {
        status: 'sent',
      };
    } catch (error) {
      this.logger.error(error);
      return {
        status: 'failed',
        error: error.message,
      };
    }
  }
}
