export type MailerPayload = {
  to: string | string[];
  from: string;
  replyTo?: string;
  replyToName?: string;
  bccEmail?: string;
  bccName?: string;
  subject: string;
  text?: string;
  html?: string;
  attachments?: { filename: string; content: Buffer }[];
};

export type MailSendResult = {
  status: 'sent' | 'failed';
  error?: string;
};

export interface IMailerService {
  sendMail(payload: MailerPayload): Promise<MailSendResult>;
}
