import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { Transporter } from 'nodemailer';
import SMTPTransport from 'nodemailer/lib/smtp-transport';
import {
  IMailerService,
  MailerPayload,
  MailSendResult,
} from './mailer.interface';
@Injectable()
export class SmtpMailerService implements IMailerService {
  private readonly logger = new Logger(SmtpMailerService.name);
  _transporter: Transporter<
    SMTPTransport.SentMessageInfo,
    SMTPTransport.Options
  >;

  constructor(private readonly configService: ConfigService) {
    this._transporter = nodemailer.createTransport({
      host: this.configService.get('mail.host'),
      port: Number(this.configService.get('mail.port')),
      secure: this.configService.get('mail.secure'), // true for port 465, false for other ports
      requireTLS: this.configService.get('mail.requireTLS'),
      auth: {
        user: this.configService.get('mail.user'),
        pass: this.configService.get('mail.pass'),
      },
    });
  }

  async sendMail(payload: MailerPayload): Promise<MailSendResult> {
    try {
      const {
        to,
        from,
        replyTo,
        replyToName,
        subject,
        text,
        html,
        attachments,
      } = payload;
      const info = await this._transporter.sendMail({
        from: from, // sender address
        to: to, // list of receivers
        subject: subject, // Subject line
        text: text, // plain text body
        html: html, // html body`
        attachments:
          attachments?.map((attachment) => ({
            filename: attachment.filename,
            content: attachment.content,
          })) || undefined,
        replyTo:
          replyToName && replyTo
            ? {
                name: replyToName,
                address: replyTo,
              }
            : replyTo,
      });
      this.logger.log('Message sent: %s', info.messageId);
      return {
        status: 'sent',
      };
    } catch (error) {
      this.logger.error(error);
      return {
        status: 'failed',
        error: error.message,
      };
    }
  }
}
