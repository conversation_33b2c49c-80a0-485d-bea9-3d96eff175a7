import { Injectable, Logger, Optional } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IStorageProvider } from '../interfaces/storage-provider.interface';
import { LocalStorageProvider } from '../providers/local-storage.provider';
import { S3StorageProvider } from '../providers/s3-storage.provider';
import { CloudinaryStorageProvider } from '../providers/cloudinary-storage.provider';
import { StorageProvider } from '../enums/storage-provider.enum';
import { StorageConfigType } from '@app/modules/storage/storage.config';

@Injectable()
export class StorageFactoryService {
  private readonly logger = new Logger(StorageFactoryService.name);
  private storageProvider: IStorageProvider;

  constructor(
    private readonly configService: ConfigService<{
      storage: StorageConfigType;
    }>,
    private readonly localStorageProvider: LocalStorageProvider,
    private readonly s3StorageProvider: S3StorageProvider,
    private readonly cloudinaryStorageProvider: CloudinaryStorageProvider,
    @Optional() private readonly moduleOptions?: any,
  ) {
    this.initializeStorageProvider();
  }

  /**
   * Initialize the storage provider based on configuration
   */
  private initializeStorageProvider(): void {
    const provider = this.configService.get('storage.provider', {
      infer: true,
    });

    switch (provider) {
      case StorageProvider.S3:
        this.logger.log('Using AWS S3 storage provider');
        this.storageProvider = this.s3StorageProvider;
        break;
      case StorageProvider.CLOUDINARY:
        this.logger.log('Using Cloudinary storage provider');
        this.storageProvider = this.cloudinaryStorageProvider;
        break;
      case StorageProvider.LOCAL:
      default:
        this.logger.log('Using local storage provider');
        this.storageProvider = this.localStorageProvider;
        break;
    }
  }

  /**
   * Get the storage provider
   * @returns Storage provider
   */
  getStorageProvider(): IStorageProvider {
    return this.storageProvider;
  }
}
