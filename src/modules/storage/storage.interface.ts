import { StorageProvider } from '@app/modules/storage/enums/storage-provider.enum';
import { StorageConfigType } from '@app/modules/storage/storage.config';
import { Type } from '@nestjs/common';
import { ModuleMetadata } from '@nestjs/common/interfaces';

/**
 * Storage module configuration options
 */
export interface StorageModuleOptions {
  /**
   * Make the module global
   */
  isGlobal?: boolean;

  /**
   * Storage provider configuration
   */
  provider?: StorageProvider;

  /**
   * Custom storage configuration that overrides environment config
   */
  config?: Partial<StorageConfigType>;

  /**
   * Enable specific storage providers
   */
  enabledProviders?: StorageProvider[];

  /**
   * Custom token for this module instance
   */
  token?: string | symbol;
}

/**
 * Factory interface for creating storage options
 */
export interface StorageOptionsFactory {
  createStorageOptions(): Promise<StorageModuleOptions> | StorageModuleOptions;
}

/**
 * Async options for storage module
 */
export interface StorageAsyncOptions extends Pick<ModuleMetadata, 'imports'> {
  /**
   * Make the module global
   */
  isGlobal?: boolean;

  /**
   * Custom token for this module instance
   */
  token?: string | symbol;

  /**
   * Factory function to create options
   */
  useFactory?: (
    ...args: any[]
  ) => Promise<StorageModuleOptions> | StorageModuleOptions;

  /**
   * Dependencies to inject into the factory function
   */
  inject?: any[];

  /**
   * Class to use for creating options
   */
  useClass?: Type<StorageOptionsFactory>;

  /**
   * Existing provider to use for creating options
   */
  useExisting?: Type<StorageOptionsFactory>;
}
