import { StorageFactoryService } from '@app/modules/storage/services/storage-factory.service';
import storageConfig from '@app/modules/storage/storage.config';
import { DynamicModule, Module, Provider } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { StorageProvider } from './enums/storage-provider.enum';
import { CloudinaryModule } from './modules/cloudinary/cloudinary.module';
import { LocalStorageModule } from './modules/local/local-storage.module';
import { S3Module } from './modules/s3/s3.module';
import { CloudinaryStorageProvider } from './providers/cloudinary-storage.provider';
import { LocalStorageProvider } from './providers/local-storage.provider';
import { S3StorageProvider } from './providers/s3-storage.provider';
import {
  StorageAsyncOptions,
  StorageModuleOptions,
  StorageOptionsFactory,
} from '@app/modules/storage/storage.interface';
import { STORAGE_MODULE_OPTIONS } from '@app/modules/storage/storage.constant';

@Module({})
export class StorageModule {
  /**
   * Register the storage module with static options
   */
  static forRoot(options: StorageModuleOptions = {}): DynamicModule {
    const optionsProvider: Provider = {
      provide: STORAGE_MODULE_OPTIONS,
      useValue: options,
    };

    return {
      global: options.isGlobal,
      module: StorageModule,
      imports: [
        ConfigModule.forRoot({
          load: [storageConfig],
        }),
        ...this.getImports(options),
      ],
      providers: [optionsProvider, ...this.getProviders(options)],
      exports: this.getExports(options),
    };
  }

  /**
   * Register the storage module with async options
   */
  static forRootAsync(options: StorageAsyncOptions): DynamicModule {
    // Import all storage modules for registerAsync since we can't determine
    // which ones are enabled until the async factory runs
    const allStorageModules = [CloudinaryModule, LocalStorageModule, S3Module];

    return {
      global: options.isGlobal,
      module: StorageModule,
      imports: [
        ConfigModule.forRoot({
          load: [storageConfig],
        }),
        ...(options.imports || []),
        ...allStorageModules,
      ],
      providers: [
        ...this.createAsyncProviders(options),
        StorageFactoryService,
        CloudinaryStorageProvider,
        LocalStorageProvider,
        S3StorageProvider,
      ],
      exports: [
        StorageFactoryService,
        CloudinaryStorageProvider,
        LocalStorageProvider,
        S3StorageProvider,
      ],
    };
  }

  /**
   * Get imports based on enabled options (used for forRoot() method)
   */
  private static getImports(options: StorageModuleOptions): any[] {
    const imports: any[] = [];
    const enabledProviders = options.enabledProviders || [
      StorageProvider.LOCAL,
      StorageProvider.S3,
      StorageProvider.CLOUDINARY,
    ];

    if (enabledProviders.includes(StorageProvider.LOCAL)) {
      imports.push(LocalStorageModule);
    }

    if (enabledProviders.includes(StorageProvider.S3)) {
      imports.push(S3Module);
    }

    if (enabledProviders.includes(StorageProvider.CLOUDINARY)) {
      imports.push(CloudinaryModule);
    }

    return imports;
  }

  /**
   * Get providers based on enabled options
   */
  private static getProviders(options: StorageModuleOptions): Provider[] {
    const providers: Provider[] = [StorageFactoryService];
    const enabledProviders = options.enabledProviders || [
      StorageProvider.LOCAL,
      StorageProvider.S3,
      StorageProvider.CLOUDINARY,
    ];

    if (enabledProviders.includes(StorageProvider.LOCAL)) {
      providers.push(LocalStorageProvider);
    }

    if (enabledProviders.includes(StorageProvider.S3)) {
      providers.push(S3StorageProvider);
    }

    if (enabledProviders.includes(StorageProvider.CLOUDINARY)) {
      providers.push(CloudinaryStorageProvider);
    }

    return providers;
  }

  /**
   * Get exports based on enabled options
   */
  private static getExports(options: StorageModuleOptions): any[] {
    const exports: any[] = [StorageFactoryService];
    const enabledProviders = options.enabledProviders || [
      StorageProvider.LOCAL,
      StorageProvider.S3,
      StorageProvider.CLOUDINARY,
    ];

    if (enabledProviders.includes(StorageProvider.LOCAL)) {
      exports.push(LocalStorageProvider);
    }

    if (enabledProviders.includes(StorageProvider.S3)) {
      exports.push(S3StorageProvider);
    }

    if (enabledProviders.includes(StorageProvider.CLOUDINARY)) {
      exports.push(CloudinaryStorageProvider);
    }

    return exports;
  }

  /**
   * Create async providers for options
   */
  private static createAsyncProviders(
    options: StorageAsyncOptions,
  ): Provider[] {
    if (options.useFactory) {
      return [
        {
          provide: STORAGE_MODULE_OPTIONS,
          useFactory: options.useFactory,
          inject: options.inject || [],
        },
      ];
    }

    if (options.useClass) {
      return [
        {
          provide: STORAGE_MODULE_OPTIONS,
          useFactory: async (optionsFactory: StorageOptionsFactory) =>
            await optionsFactory.createStorageOptions(),
          inject: [options.useClass],
        },
        {
          provide: options.useClass,
          useClass: options.useClass,
        },
      ];
    }

    if (options.useExisting) {
      return [
        {
          provide: STORAGE_MODULE_OPTIONS,
          useFactory: async (optionsFactory: StorageOptionsFactory) =>
            await optionsFactory.createStorageOptions(),
          inject: [options.useExisting],
        },
      ];
    }

    throw new Error(
      'Invalid configuration. Must provide useFactory, useClass, or useExisting.',
    );
  }
}
