/**
 * Interface for storage providers
 */
export interface IStorageProvider {
  /**
   * Upload a file to storage
   * @param file - File buffer
   * @param path - Path to store the file
   * @param options - Upload options
   * @returns Upload result
   */
  uploadFile(
    file: Buffer,
    path: string,
    options?: Record<string, any>,
  ): Promise<{
    url: string;
    path: string;
    cdnUrl?: string;
    metadata?: Record<string, any>;
  }>;

  /**
   * Delete a file from storage
   * @param path - Path of the file to delete
   * @returns Deletion result
   */
  deleteFile(path: string): Promise<boolean>;

  /**
   * Get a signed URL for a file
   * @param path - Path of the file
   * @param expiresIn - Expiration time in seconds
   * @returns Signed URL
   */
  getSignedUrl(path: string, expiresIn?: number): Promise<string>;

  /**
   * Get the public URL for a file
   * @param path - Path of the file
   * @returns Public URL
   */
  getPublicUrl(path: string): string;
}
