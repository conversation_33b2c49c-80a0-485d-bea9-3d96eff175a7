# Dynamic Storage Module

The StorageModule has been enhanced to be a **dynamic module** that supports flexible configuration through both environment variables and passed options. This allows you to configure storage providers based on environment, runtime conditions, or application-specific needs.

## Features

- 🔧 **Dynamic Configuration**: Configure via options passed to `forRoot()` or `forRootAsync()`
- 🌍 **Environment Integration**: Load configuration from environment variables
- 📦 **Selective Providers**: Enable only the storage providers you need
- ⚡ **Async Configuration**: Support for async configuration loading
- 🔄 **Runtime Flexibility**: Switch storage providers based on runtime conditions
- 🎯 **TypeScript Support**: Full type safety with comprehensive interfaces

## Quick Start

### Basic Usage

Use the default environment configuration:

```typescript
@Module({
  imports: [
    StorageModule.forRoot()
  ],
})
export class AppModule {}
```

### Enable Specific Providers

Only enable the storage providers you need:

```typescript
@Module({
  imports: [
    StorageModule.forRoot({
      enabledProviders: [StorageProvider.S3, StorageProvider.LOCAL],
      isGlobal: true,
    })
  ],
})
export class AppModule {}
```

### Override Configuration

Override specific configuration while keeping environment defaults:

```typescript
@Module({
  imports: [
    StorageModule.forRoot({
      provider: StorageProvider.S3,
      config: {
        s3: {
          region: 'us-west-2',
          bucket: 'my-custom-bucket',
          cdnDomain: 'https://cdn.example.com',
        },
      },
    })
  ],
})
export class AppModule {}
```

## Configuration Options

### StorageModuleOptions

```typescript
interface StorageModuleOptions {
  /** Make the module global */
  isGlobal?: boolean;
  
  /** Primary storage provider to use */
  provider?: StorageProvider;
  
  /** Custom configuration that overrides environment config */
  config?: Partial<StorageConfigType>;
  
  /** Enable specific storage providers */
  enabledProviders?: StorageProvider[];
  
  /** Custom token for this module instance */
  token?: string | symbol;
}
```

### Available Storage Providers

- `StorageProvider.LOCAL` - Local file system storage
- `StorageProvider.S3` - Amazon S3 compatible storage
- `StorageProvider.CLOUDINARY` - Cloudinary cloud storage

## Async Configuration

### Using Factory Function

Perfect for loading configuration from ConfigService:

```typescript
@Module({
  imports: [
    StorageModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        provider: configService.get('STORAGE_PROVIDER') as StorageProvider,
        enabledProviders: [StorageProvider.S3],
        config: {
          s3: {
            region: configService.get('AWS_REGION'),
            bucket: configService.get('AWS_S3_BUCKET'),
          },
        },
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

### Using Configuration Class

Create a dedicated configuration service:

```typescript
@Injectable()
export class StorageConfigService implements StorageOptionsFactory {
  constructor(private configService: ConfigService) {}

  createStorageOptions(): StorageModuleOptions {
    const environment = this.configService.get('NODE_ENV');
    
    if (environment === 'production') {
      return {
        provider: StorageProvider.S3,
        enabledProviders: [StorageProvider.S3],
        config: {
          s3: {
            region: this.configService.get('AWS_REGION'),
            bucket: this.configService.get('AWS_S3_BUCKET'),
          },
        },
      };
    }
    
    return {
      provider: StorageProvider.LOCAL,
      enabledProviders: [StorageProvider.LOCAL],
    };
  }
}

@Module({
  imports: [
    StorageModule.forRootAsync({
      imports: [ConfigModule],
      useClass: StorageConfigService,
    }),
  ],
})
export class AppModule {}
```

## Environment-Based Configuration

Configure different storage providers based on environment:

```typescript
@Module({
  imports: [
    StorageModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const environment = configService.get('NODE_ENV');

        switch (environment) {
          case 'development':
            return {
              provider: StorageProvider.LOCAL,
              enabledProviders: [StorageProvider.LOCAL],
              config: {
                local: {
                  uploadDir: 'dev-uploads',
                  baseUrl: 'http://localhost:3000',
                },
              },
            };

          case 'production':
            return {
              provider: StorageProvider.S3,
              enabledProviders: [StorageProvider.S3],
              config: {
                s3: {
                  region: configService.getOrThrow('AWS_REGION'),
                  bucket: configService.getOrThrow('AWS_S3_BUCKET'),
                  cdnDomain: configService.get('AWS_CLOUDFRONT_DOMAIN'),
                },
              },
            };

          default:
            return { enabledProviders: [StorageProvider.LOCAL] };
        }
      },
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

## Using in Services

### Basic Usage

```typescript
@Injectable()
export class FileUploadService {
  constructor(
    private readonly storageFactory: StorageFactoryService,
  ) {}

  async uploadFile(file: Buffer, filename: string): Promise<string> {
    const storage = this.storageFactory.getStorageProvider();
    const result = await storage.uploadFile(file, filename);
    return result.url;
  }
}
```

### Using Specific Providers

```typescript
@Injectable()
export class FileUploadService {
  constructor(
    private readonly storageFactory: StorageFactoryService,
    private readonly localStorage: LocalStorageProvider,
    private readonly s3Storage: S3StorageProvider,
  ) {}

  async uploadToS3(file: Buffer, filename: string): Promise<string> {
    const result = await this.s3Storage.uploadFile(file, filename);
    return result.url;
  }

  async uploadToLocal(file: Buffer, filename: string): Promise<string> {
    const result = await this.localStorage.uploadFile(file, filename);
    return result.url;
  }
}
```

## Configuration Merging

The module merges configuration in the following order (higher priority overrides lower):

1. **Environment Variables** (from `storage.config.ts`) - Base configuration
2. **Module Options** (passed to `forRoot()` or `forRootAsync()`) - Override specific values

This allows you to:
- Keep sensitive credentials in environment variables
- Override specific settings like bucket names or regions
- Maintain different configurations per environment

## Best Practices

### 1. Environment-Specific Configuration

```typescript
// Development - use local storage
if (environment === 'development') {
  return {
    provider: StorageProvider.LOCAL,
    enabledProviders: [StorageProvider.LOCAL],
  };
}

// Production - use cloud storage with CDN
if (environment === 'production') {
  return {
    provider: StorageProvider.S3,
    enabledProviders: [StorageProvider.S3],
    config: {
      s3: {
        cdnDomain: configService.get('AWS_CLOUDFRONT_DOMAIN'),
      },
    },
  };
}
```

### 2. Selective Provider Loading

Only load the providers you need to reduce memory footprint:

```typescript
StorageModule.forRoot({
  enabledProviders: [StorageProvider.S3], // Only load S3 provider
})
```

### 3. Global Module

Make the module global to avoid importing it in every module:

```typescript
StorageModule.forRoot({
  isGlobal: true,
})
```

### 4. Type Safety

Use TypeScript for configuration to catch errors at compile time:

```typescript
const storageConfig: StorageModuleOptions = {
  provider: StorageProvider.S3, // TypeScript will validate this
  config: {
    s3: {
      region: 'us-east-1', // TypeScript will validate this structure
      bucket: 'my-bucket',
    },
  },
};
```

## Migration from Static Module

If you're upgrading from the static StorageModule:

### Before (Static)
```typescript
@Module({
  imports: [StorageModule],
})
export class AppModule {}
```

### After (Dynamic)
```typescript
@Module({
  imports: [
    StorageModule.forRoot(), // Same behavior as before
  ],
})
export class AppModule {}
```

The dynamic module maintains backward compatibility when using `forRoot()` without options.

## Environment Variables

The module still respects all the original environment variables from `storage.config.ts`:

```bash
# Storage Provider
STORAGE_PROVIDER=s3

# AWS S3
AWS_REGION=us-east-1
AWS_S3_BUCKET=my-bucket
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_ENDPOINT=optional-custom-endpoint
AWS_CLOUDFRONT_DOMAIN=optional-cdn-domain

# Cloudinary
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
CLOUDINARY_FOLDER=assets
CLOUDINARY_SECURE=true

# Local Storage
LOCAL_STORAGE_UPLOAD_DIR=uploads
LOCAL_STORAGE_BASE_URL=http://localhost:3000
```

## Advanced Examples

See `storage.usage.example.ts` for comprehensive examples including:
- Multiple storage instances
- Conditional provider selection
- Runtime configuration switching
- Service integration patterns

## API Reference

- `StorageModule.forRoot(options?)` - Static configuration
- `StorageModule.forRootAsync(options)` - Async configuration
- `StorageFactoryService` - Access the configured storage provider
- `LocalStorageProvider` - Direct access to local storage
- `S3StorageProvider` - Direct access to S3 storage
- `CloudinaryStorageProvider` - Direct access to Cloudinary storage 