# Local Storage Module

The Local Storage Module provides file storage capabilities using the local filesystem. It's designed as a dynamic NestJS module that can be configured in multiple ways to suit different application needs.

## Features

- Store files on the local filesystem
- Generate public URLs for stored files
- Support for multiple storage instances with different configurations
- Flexible configuration options (static, async, environment variables)
- File operations: upload, delete, copy, move, list, etc.

## Installation

This module is part of the application and doesn't require separate installation.

## Configuration Options

The module accepts the following configuration options:

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `uploadDir` | string | 'uploads' | Directory where files will be uploaded |
| `baseUrl` | string | 'http://localhost:3000' | Base URL for generating public URLs |
| `isGlobal` | boolean | false | Whether to register the module as global |
| `token` | string \| symbol | undefined | Custom injection token for multiple instances |

## Usage

### Basic Usage

The simplest way to use the module is to import it without any configuration:

```typescript
import { Module } from '@nestjs/common';
import { LocalStorageModule } from './modules/storage/modules/local';

@Module({
  imports: [LocalStorageModule],
})
export class AppModule {}
```

This will use environment variables or default values for configuration.

### Static Configuration

You can provide static configuration options using the `forRoot` method:

```typescript
import { Module } from '@nestjs/common';
import { LocalStorageModule } from './modules/storage/modules/local';

@Module({
  imports: [
    LocalStorageModule.forRoot({
      uploadDir: 'uploads/images',
      baseUrl: 'https://example.com',
      isGlobal: true,
    }),
  ],
})
export class AppModule {}
```

### Async Configuration

For more complex scenarios where configuration values need to be loaded asynchronously:

#### Using Factory Function

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LocalStorageModule } from './modules/storage/modules/local';

@Module({
  imports: [
    ConfigModule.forRoot(),
    LocalStorageModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        uploadDir: configService.get('STORAGE_UPLOAD_DIR'),
        baseUrl: configService.get('STORAGE_BASE_URL'),
        isGlobal: true,
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

#### Using Class

```typescript
import { Injectable, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LocalStorageModule, LocalStorageOptionsFactory, LocalStorageModuleOptions } from './modules/storage/modules/local';

@Injectable()
class LocalStorageConfigService implements LocalStorageOptionsFactory {
  constructor(private configService: ConfigService) {}
  
  createLocalStorageOptions(): LocalStorageModuleOptions {
    return {
      uploadDir: this.configService.get('STORAGE_UPLOAD_DIR'),
      baseUrl: this.configService.get('STORAGE_BASE_URL'),
    };
  }
}

@Module({
  imports: [
    ConfigModule.forRoot(),
    LocalStorageModule.forRootAsync({
      imports: [ConfigModule],
      useClass: LocalStorageConfigService,
    }),
  ],
})
export class AppModule {}
```

#### Using Existing Provider

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { LocalStorageModule } from './modules/storage/modules/local';
import { StorageConfigService } from './storage-config.service';

@Module({
  imports: [
    ConfigModule.forRoot(),
    LocalStorageModule.forRootAsync({
      imports: [ConfigModule],
      useExisting: StorageConfigService,
    }),
  ],
  providers: [StorageConfigService],
})
export class AppModule {}
```

### Multiple Storage Instances

You can create multiple instances of the LocalStorageModule with different configurations:

#### Using forRoot with Token

```typescript
import { Module } from '@nestjs/common';
import { LocalStorageModule } from './modules/storage/modules/local';

@Module({
  imports: [
    // Default storage for general files
    LocalStorageModule.forRoot({
      uploadDir: 'uploads/general',
    }),
    // Specific storage for avatars
    LocalStorageModule.forRoot({
      uploadDir: 'uploads/avatars',
      token: 'AVATAR_STORAGE',
    }),
  ],
})
export class AppModule {}
```

#### Using forFeature

```typescript
import { Module } from '@nestjs/common';
import { LocalStorageModule } from './modules/storage/modules/local';

@Module({
  imports: [
    // Default storage
    LocalStorageModule.forRoot(),
    // Specific storage for avatars
    LocalStorageModule.forFeature('AVATAR_STORAGE', {
      uploadDir: 'uploads/avatars',
      baseUrl: 'https://example.com/avatars',
    }),
  ],
})
export class AppModule {}
```

#### Using forFeatureAsync

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LocalStorageModule } from './modules/storage/modules/local';

@Module({
  imports: [
    ConfigModule.forRoot(),
    // Default storage
    LocalStorageModule.forRoot(),
    // Specific storage for avatars with async config
    LocalStorageModule.forFeatureAsync('AVATAR_STORAGE', {
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        uploadDir: configService.get('AVATAR_UPLOAD_DIR'),
        baseUrl: configService.get('AVATAR_BASE_URL'),
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

## Injecting the Service

### Default Instance

```typescript
import { Injectable } from '@nestjs/common';
import { LocalStorageService } from './modules/storage/modules/local';

@Injectable()
export class AppService {
  constructor(private readonly storageService: LocalStorageService) {}

  async uploadFile(file: Buffer, filename: string) {
    return this.storageService.uploadFile(file, filename);
  }
}
```

### Named Instance

```typescript
import { Inject, Injectable } from '@nestjs/common';
import { LocalStorageService } from './modules/storage/modules/local';

@Injectable()
export class UserService {
  constructor(
    // Default storage service
    private readonly storageService: LocalStorageService,
    // Avatar-specific storage service
    @Inject('LocalStorageService_AVATAR_STORAGE')
    private readonly avatarStorage: LocalStorageService,
  ) {}

  async uploadAvatar(userId: string, file: Buffer) {
    return this.avatarStorage.uploadFile(file, `${userId}.jpg`);
  }
}
```

### Using LocalStorageFactory

```typescript
import { Injectable } from '@nestjs/common';
import { LocalStorageService, LocalStorageFactory } from './modules/storage/modules/local';

@Injectable()
export class UserService {
  constructor(
    // Default storage service
    private readonly storageService: LocalStorageService,
    // Avatar-specific storage service using factory helper
    @LocalStorageFactory.get('AVATAR_STORAGE')
    private readonly avatarStorage: LocalStorageService,
  ) {}

  async uploadAvatar(userId: string, file: Buffer) {
    return this.avatarStorage.uploadFile(file, `${userId}.jpg`);
  }
}
```

## API Reference

### LocalStorageService

The `LocalStorageService` provides the following methods:

- `uploadFile(file: Buffer, filePath: string, options?: Record<string, any>)`: Upload a file
- `deleteFile(filePath: string): Promise<boolean>`: Delete a file
- `getPublicUrl(filePath: string): string`: Get public URL for a file
- `fileExists(filePath: string): Promise<boolean>`: Check if a file exists
- `getFileMetadata(filePath: string): Promise<{ size: number; lastModified: Date; created: Date; } | null>`: Get file metadata
- `listFiles(directoryPath?: string): Promise<string[]>`: List files in a directory
- `createDirectory(directoryPath: string): Promise<boolean>`: Create a directory
- `deleteDirectory(directoryPath: string): Promise<boolean>`: Delete a directory
- `copyFile(sourcePath: string, destinationPath: string): Promise<boolean>`: Copy a file
- `moveFile(sourcePath: string, destinationPath: string): Promise<boolean>`: Move a file
- `getFileContent(filePath: string): Promise<Buffer | null>`: Get file content as buffer

### LocalStorageFactory

The `LocalStorageFactory` provides the following static methods:

- `get(token: string | symbol): any`: Get a decorator to inject a specific instance of LocalStorageService
- `createServiceToken(name: string | symbol): string`: Create a custom injection token for a LocalStorageService instance
- `createOptionsToken(name: string | symbol): string`: Create a custom options token for a LocalStorageService instance

## Environment Variables

When using the default configuration, the module will look for the following environment variables:

- `STORAGE_LOCAL_UPLOAD_DIR`: Directory where files will be uploaded (default: 'uploads')
- `STORAGE_LOCAL_BASE_URL`: Base URL for generating public URLs (default: 'http://localhost:3000')