import { registerAs } from '@nestjs/config';
import { IsOptional, IsString } from 'class-validator';
import validateConfig from '@app/common/config/validate-config';

export type LocalStorageConfigType = {
  uploadDir: string;
  baseUrl: string;
};

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  LOCAL_STORAGE_UPLOAD_DIR: string;

  @IsString()
  @IsOptional()
  LOCAL_STORAGE_BASE_URL: string;
}

export default registerAs<LocalStorageConfigType>('localStorage', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);
  return {
    uploadDir: process.env.LOCAL_STORAGE_UPLOAD_DIR || 'uploads',
    baseUrl: process.env.LOCAL_STORAGE_BASE_URL || 'http://localhost:3000',
  };
});
