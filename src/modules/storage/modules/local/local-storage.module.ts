import { DynamicModule, Module, Provider } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LocalStorageService } from './local-storage.service';
import {
  LOCAL_STORAGE_OPTIONS,
  LocalStorageModuleOptions,
  LocalStorageAsyncOptions,
  LocalStorageOptionsFactory,
  createLocalStorageOptionsToken,
} from './local-storage.interfaces';
import localStorageConfig from './local-storage.config';

/**
 * LocalStorageModule provides file storage capabilities using the local filesystem.
 *
 * This module can be configured in multiple ways:
 *
 * 1. Static configuration using `forRoot()` method:
 *    ```typescript
 *    // Basic usage
 *    LocalStorageModule.forRoot({
 *      uploadDir: 'custom-uploads',
 *      baseUrl: 'https://example.com',
 *      isGlobal: true
 *    })
 *
 *    // With custom token for multiple instances
 *    LocalStorageModule.forRoot({
 *      uploadDir: 'avatars',
 *      token: 'AVATAR_STORAGE'
 *    })
 *    ```
 *
 * 2. Async configuration using `forRootAsync()` method:
 *    ```typescript
 *    // Using factory function
 *    LocalStorageModule.forRootAsync({
 *      imports: [ConfigModule],
 *      useFactory: (configService: ConfigService) => ({
 *        uploadDir: configService.get('UPLOAD_DIR'),
 *        baseUrl: configService.get('BASE_URL')
 *      }),
 *      inject: [ConfigService]
 *    })
 *
 *    // Using class
 *    LocalStorageModule.forRootAsync({
 *      useClass: LocalStorageConfigService
 *    })
 *
 *    // Using existing provider
 *    LocalStorageModule.forRootAsync({
 *      useExisting: ConfigService
 *    })
 *    ```
 *
 * 3. Default configuration using environment variables:
 *    ```typescript
 *    // Uses environment variables or defaults
 *    LocalStorageModule
 *    ```
 *
 * 4. Feature-specific instances using `forFeature()` method:
 *    ```typescript
 *    // Create a specific instance with its own configuration
 *    LocalStorageModule.forFeature('AVATAR_STORAGE', {
 *      uploadDir: 'avatars'
 *    })
 *    ```
 */

@Module({
  imports: [ConfigModule.forFeature(localStorageConfig)],
  providers: [LocalStorageService],
  exports: [LocalStorageService],
})
export class LocalStorageModule {
  /**
   * Get a specific instance of LocalStorageService using a custom token.
   *
   * This method allows you to create multiple instances of the LocalStorageModule
   * with different configurations. This is useful when you need to store different
   * types of files in different locations.
   *
   * Example:
   * ```typescript
   * @Module({
   *   imports: [
   *     // Main storage for general files
   *     LocalStorageModule.forRoot({
   *       uploadDir: 'uploads/general',
   *       baseUrl: 'https://example.com/files'
   *     }),
   *     // Specific storage for avatars
   *     LocalStorageModule.forFeature('AVATAR_STORAGE', {
   *       uploadDir: 'uploads/avatars',
   *       baseUrl: 'https://example.com/avatars'
   *     })
   *   ]
   * })
   * export class AppModule {}
   *
   * // In your service:
   * @Injectable()
   * export class UserService {
   *   constructor(
   *     private readonly storageService: LocalStorageService,
   *     @Inject('LocalStorageService_AVATAR_STORAGE')
   *     private readonly avatarStorage: LocalStorageService
   *   ) {}
   *
   *   // Or using the factory helper:
   *   constructor(
   *     private readonly storageService: LocalStorageService,
   *     @LocalStorageFactory.get('AVATAR_STORAGE')
   *     private readonly avatarStorage: LocalStorageService
   *   ) {}
   * }
   * ```
   *
   * @param token The token used to identify the specific instance
   * @param options Optional configuration options for this specific instance
   * @returns A dynamic module with a specific LocalStorageService instance
   */
  static forFeature(
    token: string | symbol,
    options: LocalStorageModuleOptions = {},
  ): DynamicModule {
    const tokenString =
      typeof token === 'symbol' ? token.description : token.toString();
    const optionsToken = createLocalStorageOptionsToken(tokenString);
    const serviceToken = `LocalStorageService_${tokenString}`;

    // Create a provider for the options
    const optionsProvider = {
      provide: optionsToken,
      useValue: options,
    };

    // Create a provider for the specific instance
    const serviceProvider = {
      provide: serviceToken,
      useFactory: (
        options: LocalStorageModuleOptions,
        configService: ConfigService,
      ) => {
        return new LocalStorageService(configService, options);
      },
      inject: [optionsToken, ConfigService],
    };

    return {
      module: LocalStorageModule,
      providers: [optionsProvider, serviceProvider],
      exports: [serviceToken],
    };
  }

  /**
   * Get a specific instance of LocalStorageService using a custom token with async options.
   *
   * This method allows you to create multiple instances of the LocalStorageModule
   * with different configurations loaded asynchronously. This is useful when you need
   * to store different types of files in different locations and the configuration
   * needs to be loaded from a service or other async source.
   *
   * Example:
   * ```typescript
   * @Module({
   *   imports: [
   *     // Main storage for general files
   *     LocalStorageModule.forRoot(),
   *     // Specific storage for avatars with async config
   *     LocalStorageModule.forFeatureAsync('AVATAR_STORAGE', {
   *       imports: [ConfigModule],
   *       useFactory: (configService: ConfigService) => ({
   *         uploadDir: configService.get('AVATAR_UPLOAD_DIR'),
   *         baseUrl: configService.get('AVATAR_BASE_URL')
   *       }),
   *       inject: [ConfigService]
   *     })
   *   ]
   * })
   * export class AppModule {}
   *
   * // In your service:
   * @Injectable()
   * export class UserService {
   *   constructor(
   *     @LocalStorageFactory.get('AVATAR_STORAGE')
   *     private readonly avatarStorage: LocalStorageService
   *   ) {}
   *
   *   async uploadAvatar(userId: string, file: Buffer) {
   *     return this.avatarStorage.uploadFile(file, `${userId}.jpg`);
   *   }
   * }
   * ```
   *
   * @param token The token used to identify the specific instance
   * @param asyncOptions Async configuration options for this specific instance
   * @returns A dynamic module with a specific LocalStorageService instance
   */
  static forFeatureAsync(
    token: string | symbol,
    asyncOptions: LocalStorageAsyncOptions,
  ): DynamicModule {
    const tokenString =
      typeof token === 'symbol' ? token.description : token.toString();
    const optionsToken = createLocalStorageOptionsToken(tokenString);
    const serviceToken = `LocalStorageService_${tokenString}`;

    const imports = [ConfigModule.forFeature(localStorageConfig)];
    if (asyncOptions.imports?.length) {
      imports.push(...asyncOptions.imports);
    }

    // Create async providers for the options
    const optionsProviders = this.createAsyncProviders(
      asyncOptions,
      optionsToken,
    );

    // Create a provider for the specific instance
    const serviceProvider = {
      provide: serviceToken,
      useFactory: (
        options: LocalStorageModuleOptions,
        configService: ConfigService,
      ) => {
        return new LocalStorageService(configService, options);
      },
      inject: [optionsToken, ConfigService],
    };

    return {
      module: LocalStorageModule,
      imports,
      providers: [...optionsProviders, serviceProvider],
      exports: [serviceToken],
    };
  }

  /**
   * Register the module with static options.
   *
   * This method allows you to configure the LocalStorageModule with static options
   * at the time of module import. It's useful when you know your configuration values
   * at application bootstrap time.
   *
   * Example:
   * ```typescript
   * @Module({
   *   imports: [
   *     LocalStorageModule.forRoot({
   *       uploadDir: 'uploads/images',
   *       baseUrl: 'https://example.com',
   *       isGlobal: true
   *     })
   *   ]
   * })
   * export class AppModule {}
   * ```
   *
   * @param options Configuration options for the LocalStorageModule
   * @returns A dynamic module configuration
   */
  static forRoot(options: LocalStorageModuleOptions = {}): DynamicModule {
    // Use custom token if provided, otherwise use default token
    const token = options.token
      ? createLocalStorageOptionsToken(options.token.toString())
      : LOCAL_STORAGE_OPTIONS;

    const optionsProvider: Provider = {
      provide: token,
      useValue: options,
    };

    // Create a custom service provider with the specific token
    const serviceProvider = {
      provide: LocalStorageService,
      useFactory: (
        options: LocalStorageModuleOptions,
        configService: ConfigService,
      ) => {
        return new LocalStorageService(configService, options);
      },
      inject: [token, ConfigService],
    };

    // If a custom token is provided, create an additional named service provider
    const providers: Provider[] = [optionsProvider, serviceProvider];

    if (options.token) {
      const tokenString =
        typeof options.token === 'symbol'
          ? options.token.description
          : options.token.toString();

      const serviceToken = `LocalStorageService_${tokenString}`;

      const namedServiceProvider = {
        provide: serviceToken,
        useFactory: (
          options: LocalStorageModuleOptions,
          configService: ConfigService,
        ) => {
          return new LocalStorageService(configService, options);
        },
        inject: [token, ConfigService],
      };

      providers.push(namedServiceProvider);
    }

    const exports: any[] = [LocalStorageService];

    if (options.token) {
      const tokenString =
        typeof options.token === 'symbol'
          ? options.token.description
          : options.token.toString();
      exports.push(`LocalStorageService_${tokenString}`);
    }

    return {
      global: options.isGlobal,
      module: LocalStorageModule,
      imports: [ConfigModule.forFeature(localStorageConfig)],
      providers,
      exports,
    };
  }

  /**
   * Register the module with async options.
   *
   * This method allows you to configure the LocalStorageModule asynchronously,
   * which is useful when configuration values are not available at application bootstrap
   * time and need to be loaded from a configuration service, database, or other source.
   *
   * Example with factory function:
   * ```typescript
   * @Module({
   *   imports: [
   *     ConfigModule.forRoot(),
   *     LocalStorageModule.forRootAsync({
   *       imports: [ConfigModule],
   *       useFactory: (configService: ConfigService) => ({
   *         uploadDir: configService.get('STORAGE_UPLOAD_DIR'),
   *         baseUrl: configService.get('STORAGE_BASE_URL'),
   *         isGlobal: true
   *       }),
   *       inject: [ConfigService]
   *     })
   *   ]
   * })
   * export class AppModule {}
   * ```
   *
   * Example with class:
   * ```typescript
   * @Injectable()
   * class LocalStorageConfigService implements LocalStorageOptionsFactory {
   *   constructor(private configService: ConfigService) {}
   *
   *   createLocalStorageOptions(): LocalStorageModuleOptions {
   *     return {
   *       uploadDir: this.configService.get('STORAGE_UPLOAD_DIR'),
   *       baseUrl: this.configService.get('STORAGE_BASE_URL')
   *     };
   *   }
   * }
   *
   * @Module({
   *   imports: [
   *     LocalStorageModule.forRootAsync({
   *       imports: [ConfigModule],
   *       useClass: LocalStorageConfigService
   *     })
   *   ]
   * })
   * export class AppModule {}
   * ```
   *
   * @param options Async configuration options for the LocalStorageModule
   * @returns A dynamic module configuration
   */
  static forRootAsync(options: LocalStorageAsyncOptions): DynamicModule {
    const imports = [ConfigModule.forFeature(localStorageConfig)];

    if (options.imports?.length) {
      imports.push(...options.imports);
    }

    // Use custom token if provided, otherwise use default token
    const token = options.token
      ? createLocalStorageOptionsToken(options.token.toString())
      : LOCAL_STORAGE_OPTIONS;

    const asyncProviders = this.createAsyncProviders(options, token);

    // Create a custom service provider with the specific token
    const serviceProvider = {
      provide: LocalStorageService,
      useFactory: (
        options: LocalStorageModuleOptions,
        configService: ConfigService,
      ) => {
        return new LocalStorageService(configService, options);
      },
      inject: [token, ConfigService],
    };

    // Combine all providers
    const providers: Provider[] = [...asyncProviders, serviceProvider];

    // If a custom token is provided, create an additional named service provider
    if (options.token) {
      const tokenString =
        typeof options.token === 'symbol'
          ? options.token.description
          : options.token.toString();

      const serviceToken = `LocalStorageService_${tokenString}`;

      const namedServiceProvider = {
        provide: serviceToken,
        useFactory: (
          options: LocalStorageModuleOptions,
          configService: ConfigService,
        ) => {
          return new LocalStorageService(configService, options);
        },
        inject: [token, ConfigService],
      };

      providers.push(namedServiceProvider);
    }

    const exports: any[] = [LocalStorageService];

    if (options.token) {
      const tokenString =
        typeof options.token === 'symbol'
          ? options.token.description
          : options.token.toString();
      exports.push(`LocalStorageService_${tokenString}`);
    }

    return {
      global: options.isGlobal,
      module: LocalStorageModule,
      imports,
      providers,
      exports,
    };
  }

  private static createAsyncProviders(
    options: LocalStorageAsyncOptions,
    token: string = LOCAL_STORAGE_OPTIONS,
  ): Provider[] {
    if (options.useExisting || options.useFactory) {
      return [this.createAsyncOptionsProvider(options, token)];
    }

    // Make sure useClass is defined before using it
    if (!options.useClass) {
      throw new Error(
        'Invalid async options: useClass, useExisting, or useFactory must be provided',
      );
    }

    return [
      this.createAsyncOptionsProvider(options, token),
      {
        provide: options.useClass,
        useClass: options.useClass,
      },
    ];
  }

  private static createAsyncOptionsProvider(
    options: LocalStorageAsyncOptions,
    token: string = LOCAL_STORAGE_OPTIONS,
  ): Provider {
    if (options.useFactory) {
      return {
        provide: token,
        useFactory: options.useFactory,
        inject: options.inject || [],
      };
    }

    // Make sure either useExisting or useClass is defined
    const injectToken = options.useExisting || options.useClass;
    if (!injectToken) {
      throw new Error(
        'Invalid async options: useClass, useExisting, or useFactory must be provided',
      );
    }

    return {
      provide: token,
      useFactory: async (optionsFactory: LocalStorageOptionsFactory) =>
        await optionsFactory.createLocalStorageOptions(),
      inject: [injectToken],
    };
  }
}
