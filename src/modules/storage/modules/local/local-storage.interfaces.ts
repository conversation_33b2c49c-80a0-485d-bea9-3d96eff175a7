import { Type } from '@nestjs/common';

/**
 * Configuration options for the LocalStorageModule
 */
export interface LocalStorageModuleOptions {
  /**
   * Directory where files will be uploaded
   * @default 'uploads'
   */
  uploadDir?: string;

  /**
   * Base URL for generating public URLs
   * @default 'http://localhost:3000'
   */
  baseUrl?: string;

  /**
   * Whether to register the module as global
   * @default false
   */
  isGlobal?: boolean;

  /**
   * Custom injection token for the module
   * Allows for multiple instances with different configurations
   */
  token?: string | symbol;
}

/**
 * Factory interface for creating LocalStorageModuleOptions
 */
export interface LocalStorageOptionsFactory {
  createLocalStorageOptions():
    | Promise<LocalStorageModuleOptions>
    | LocalStorageModuleOptions;
}

/**
 * Async options for configuring the LocalStorageModule
 */
export interface LocalStorageAsyncOptions {
  /**
   * Injection token
   */
  inject?: any[];

  /**
   * Whether to register the module as global
   * @default false
   */
  isGlobal?: boolean;

  /**
   * Factory function to create options
   */
  useFactory?: (
    ...args: any[]
  ) => Promise<LocalStorageModuleOptions> | LocalStorageModuleOptions;

  /**
   * Class to use for creating options
   */
  useClass?: Type<LocalStorageOptionsFactory>;

  /**
   * Existing provider to use for options
   */
  useExisting?: Type<LocalStorageOptionsFactory>;

  /**
   * Custom injection token for the module
   * Allows for multiple instances with different configurations
   */
  token?: string | symbol;

  /**
   * Optional imports needed for this module instance
   */
  imports?: any[];
}

/**
 * Default injection token for LocalStorageModuleOptions
 */
export const LOCAL_STORAGE_OPTIONS = 'LOCAL_STORAGE_OPTIONS';

/**
 * Creates a custom injection token for LocalStorageModuleOptions
 * @param name Optional name to create a unique token
 * @returns A unique injection token
 */
export function createLocalStorageOptionsToken(name?: string): string {
  return name ? `${LOCAL_STORAGE_OPTIONS}_${name}` : LOCAL_STORAGE_OPTIONS;
}
