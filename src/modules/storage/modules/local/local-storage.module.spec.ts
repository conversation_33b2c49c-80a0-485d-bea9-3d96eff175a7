import { Test } from '@nestjs/testing';
import { LocalStorageModule } from './local-storage.module';
import { LocalStorageService } from './local-storage.service';
import {
  LOCAL_STORAGE_OPTIONS,
  LocalStorageModuleOptions,
} from './local-storage.interfaces';
import { ConfigModule, ConfigService } from '@nestjs/config';
import localStorageConfig from './local-storage.config';

describe('LocalStorageModule', () => {
  describe('forRoot', () => {
    it('should provide LocalStorageService', async () => {
      const module = await Test.createTestingModule({
        imports: [LocalStorageModule.forRoot()],
      }).compile();

      const service = module.get<LocalStorageService>(LocalStorageService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(LocalStorageService);
    });

    it('should register with custom options', async () => {
      const options: LocalStorageModuleOptions = {
        uploadDir: 'custom-uploads',
        baseUrl: 'https://example.com',
      };

      const module = await Test.createTestingModule({
        imports: [LocalStorageModule.forRoot(options)],
      }).compile();

      const service = module.get<LocalStorageService>(LocalStorageService);
      expect(service).toBeDefined();

      // Access private properties for testing
      const uploadDir = (service as any).uploadDir;
      const baseUrl = (service as any).baseUrl;

      expect(uploadDir).toBe('custom-uploads');
      expect(baseUrl).toBe('https://example.com');
    });

    it('should register as global when isGlobal is true', () => {
      const options: LocalStorageModuleOptions = {
        isGlobal: true,
      };

      const dynamicModule = LocalStorageModule.forRoot(options);
      expect(dynamicModule.global).toBe(true);
    });

    it('should not register as global by default', () => {
      const dynamicModule = LocalStorageModule.forRoot();
      expect(dynamicModule.global).toBeUndefined();
    });

    it('should inject options with LOCAL_STORAGE_OPTIONS token', async () => {
      const options: LocalStorageModuleOptions = {
        uploadDir: 'test-uploads',
        baseUrl: 'https://test.com',
      };

      const module = await Test.createTestingModule({
        imports: [LocalStorageModule.forRoot(options)],
      }).compile();

      const injectedOptions = module.get(LOCAL_STORAGE_OPTIONS);
      expect(injectedOptions).toBeDefined();
      expect(injectedOptions).toEqual(options);
    });

    it('should create a named service provider when token is provided', async () => {
      const token = 'custom-token';
      const options: LocalStorageModuleOptions = {
        token,
        uploadDir: 'token-uploads',
      };

      const module = await Test.createTestingModule({
        imports: [LocalStorageModule.forRoot(options)],
      }).compile();

      const serviceToken = `LocalStorageService_${token}`;
      const namedService = module.get(serviceToken);

      expect(namedService).toBeDefined();
      expect(namedService).toBeInstanceOf(LocalStorageService);

      // Access private properties for testing
      const uploadDir = namedService.uploadDir;
      expect(uploadDir).toBe('token-uploads');
    });
  });

  describe('backward compatibility', () => {
    it('should work with default import using environment variables', async () => {
      // Mock environment variables
      process.env.LOCAL_STORAGE_UPLOAD_DIR = 'env-uploads';
      process.env.LOCAL_STORAGE_BASE_URL = 'https://env-example.com';

      const module = await Test.createTestingModule({
        imports: [LocalStorageModule],
      }).compile();

      const service = module.get<LocalStorageService>(LocalStorageService);
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(LocalStorageService);

      // Clean up environment variables
      delete process.env.LOCAL_STORAGE_UPLOAD_DIR;
      delete process.env.LOCAL_STORAGE_BASE_URL;
    });
  });
});

describe('forRootAsync', () => {
  it('should register with useFactory', async () => {
    const options = {
      useFactory: () => ({
        uploadDir: 'factory-uploads',
        baseUrl: 'https://factory.com',
      }),
    };

    const module = await Test.createTestingModule({
      imports: [LocalStorageModule.forRootAsync(options)],
    }).compile();

    const service = module.get<LocalStorageService>(LocalStorageService);
    expect(service).toBeDefined();

    // Access private properties for testing
    const uploadDir = (service as any).uploadDir;
    const baseUrl = (service as any).baseUrl;

    expect(uploadDir).toBe('factory-uploads');
    expect(baseUrl).toBe('https://factory.com');
  });

  it('should register with useFactory and inject dependencies', async () => {
    const options = {
      imports: [ConfigModule.forFeature(localStorageConfig)],
      useFactory: (configService: ConfigService) => ({
        uploadDir: configService.get(
          'localStorage.uploadDir',
          'default-uploads',
        ),
        baseUrl: 'https://injected.com',
      }),
      inject: [ConfigService],
    };

    const module = await Test.createTestingModule({
      imports: [LocalStorageModule.forRootAsync(options)],
    }).compile();

    const service = module.get<LocalStorageService>(LocalStorageService);
    expect(service).toBeDefined();

    // Access private properties for testing
    const baseUrl = (service as any).baseUrl;
    expect(baseUrl).toBe('https://injected.com');
  });

  it('should register with useClass', async () => {
    class TestOptionsFactory {
      createLocalStorageOptions() {
        return {
          uploadDir: 'class-uploads',
          baseUrl: 'https://class.com',
        };
      }
    }

    const options = {
      useClass: TestOptionsFactory,
    };

    const module = await Test.createTestingModule({
      imports: [LocalStorageModule.forRootAsync(options)],
    }).compile();

    const service = module.get<LocalStorageService>(LocalStorageService);
    expect(service).toBeDefined();

    // Access private properties for testing
    const uploadDir = (service as any).uploadDir;
    const baseUrl = (service as any).baseUrl;

    expect(uploadDir).toBe('class-uploads');
    expect(baseUrl).toBe('https://class.com');
  });

  it('should register with useExisting', async () => {
    class TestOptionsFactory {
      createLocalStorageOptions() {
        return {
          uploadDir: 'existing-uploads',
          baseUrl: 'https://existing.com',
        };
      }
    }

    const module = await Test.createTestingModule({
      providers: [
        {
          provide: TestOptionsFactory,
          useClass: TestOptionsFactory,
        },
      ],
      imports: [
        LocalStorageModule.forRootAsync({
          useExisting: TestOptionsFactory,
        }),
      ],
    }).compile();

    const service = module.get<LocalStorageService>(LocalStorageService);
    expect(service).toBeDefined();

    // Access private properties for testing
    const uploadDir = (service as any).uploadDir;
    const baseUrl = (service as any).baseUrl;

    expect(uploadDir).toBe('existing-uploads');
    expect(baseUrl).toBe('https://existing.com');
  });

  it('should register as global when isGlobal is true', () => {
    const options = {
      useFactory: () => ({}),
      isGlobal: true,
    };

    const dynamicModule = LocalStorageModule.forRootAsync(options);
    expect(dynamicModule.global).toBe(true);
  });

  it('should not register as global by default', () => {
    const options = {
      useFactory: () => ({}),
    };

    const dynamicModule = LocalStorageModule.forRootAsync(options);
    expect(dynamicModule.global).toBeUndefined();
  });

  it('should create a named service provider when token is provided', async () => {
    const token = 'async-token';
    const options = {
      token,
      useFactory: () => ({
        uploadDir: 'token-async-uploads',
      }),
    };

    const module = await Test.createTestingModule({
      imports: [LocalStorageModule.forRootAsync(options)],
    }).compile();

    const serviceToken = `LocalStorageService_${token}`;
    const namedService = module.get(serviceToken);

    expect(namedService).toBeDefined();
    expect(namedService).toBeInstanceOf(LocalStorageService);

    // Access private properties for testing
    const uploadDir = namedService.uploadDir;
    expect(uploadDir).toBe('token-async-uploads');
  });

  it('should throw an error when no provider is specified', async () => {
    const options = {};

    await expect(
      Test.createTestingModule({
        imports: [LocalStorageModule.forRootAsync(options as any)],
      }).compile(),
    ).rejects.toThrow();
  });
});

describe('multiple module instances', () => {
  it('should support multiple instances with different configurations', async () => {
    // Create two instances with different configurations
    const firstOptions: LocalStorageModuleOptions = {
      uploadDir: 'first-uploads',
      baseUrl: 'https://first.example.com',
      token: 'first-instance',
    };

    const secondOptions: LocalStorageModuleOptions = {
      uploadDir: 'second-uploads',
      baseUrl: 'https://second.example.com',
      token: 'second-instance',
    };

    const module = await Test.createTestingModule({
      imports: [
        LocalStorageModule.forRoot(firstOptions),
        LocalStorageModule.forRoot(secondOptions),
      ],
    }).compile();

    // Get the services using their custom tokens
    const firstServiceToken = `LocalStorageService_${String(firstOptions.token)}`;
    const secondServiceToken = `LocalStorageService_${String(secondOptions.token)}`;

    const firstService = module.get(firstServiceToken);
    const secondService = module.get(secondServiceToken);

    expect(firstService).toBeDefined();
    expect(secondService).toBeDefined();
    expect(firstService).toBeInstanceOf(LocalStorageService);
    expect(secondService).toBeInstanceOf(LocalStorageService);

    // Access private properties for testing
    const firstUploadDir = firstService.uploadDir;
    const firstBaseUrl = firstService.baseUrl;
    const secondUploadDir = secondService.uploadDir;
    const secondBaseUrl = secondService.baseUrl;

    // Verify each service has its own configuration
    expect(firstUploadDir).toBe('first-uploads');
    expect(firstBaseUrl).toBe('https://first.example.com');
    expect(secondUploadDir).toBe('second-uploads');
    expect(secondBaseUrl).toBe('https://second.example.com');
  });

  it('should support multiple instances with custom injection tokens', async () => {
    const firstToken = 'custom-token-1';
    const secondToken = 'custom-token-2';

    // Create two instances with different tokens
    const module = await Test.createTestingModule({
      imports: [
        LocalStorageModule.forFeature(firstToken, {
          uploadDir: 'token1-uploads',
          baseUrl: 'https://token1.example.com',
        }),
        LocalStorageModule.forFeature(secondToken, {
          uploadDir: 'token2-uploads',
          baseUrl: 'https://token2.example.com',
        }),
      ],
    }).compile();

    // Get the services using their custom tokens
    const firstServiceToken = `LocalStorageService_${firstToken}`;
    const secondServiceToken = `LocalStorageService_${secondToken}`;

    const firstService = module.get(firstServiceToken);
    const secondService = module.get(secondServiceToken);

    expect(firstService).toBeDefined();
    expect(secondService).toBeDefined();

    // Access private properties for testing
    const firstUploadDir = firstService.uploadDir;
    const firstBaseUrl = firstService.baseUrl;
    const secondUploadDir = secondService.uploadDir;
    const secondBaseUrl = secondService.baseUrl;

    // Verify each service has its own configuration
    expect(firstUploadDir).toBe('token1-uploads');
    expect(firstBaseUrl).toBe('https://token1.example.com');
    expect(secondUploadDir).toBe('token2-uploads');
    expect(secondBaseUrl).toBe('https://token2.example.com');
  });

  it('should support async configuration with multiple instances', async () => {
    const firstToken = 'async-token-1';
    const secondToken = 'async-token-2';

    // Create two instances with different tokens and async configuration
    const module = await Test.createTestingModule({
      imports: [
        LocalStorageModule.forFeatureAsync(firstToken, {
          useFactory: () => ({
            uploadDir: 'async1-uploads',
            baseUrl: 'https://async1.example.com',
          }),
        }),
        LocalStorageModule.forFeatureAsync(secondToken, {
          useFactory: () => ({
            uploadDir: 'async2-uploads',
            baseUrl: 'https://async2.example.com',
          }),
        }),
      ],
    }).compile();

    // Get the services using their custom tokens
    const firstServiceToken = `LocalStorageService_${firstToken}`;
    const secondServiceToken = `LocalStorageService_${secondToken}`;

    const firstService = module.get(firstServiceToken);
    const secondService = module.get(secondServiceToken);

    expect(firstService).toBeDefined();
    expect(secondService).toBeDefined();

    // Access private properties for testing
    const firstUploadDir = firstService.uploadDir;
    const firstBaseUrl = firstService.baseUrl;
    const secondUploadDir = secondService.uploadDir;
    const secondBaseUrl = secondService.baseUrl;

    // Verify each service has its own configuration
    expect(firstUploadDir).toBe('async1-uploads');
    expect(firstBaseUrl).toBe('https://async1.example.com');
    expect(secondUploadDir).toBe('async2-uploads');
    expect(secondBaseUrl).toBe('https://async2.example.com');
  });
});
