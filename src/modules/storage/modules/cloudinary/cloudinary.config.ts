import { registerAs } from '@nestjs/config';
import { IsOptional, IsString } from 'class-validator';
import validateConfig from '@app/common/config/validate-config';

export type CloudinaryConfigType = {
  cloudName: string;
  apiKey: string;
  apiSecret: string;
  folder: string;
  secure: boolean;
};

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  CLOUDINARY_CLOUD_NAME: string;

  @IsString()
  @IsOptional()
  CLOUDINARY_API_KEY: string;

  @IsString()
  @IsOptional()
  CLOUDINARY_API_SECRET: string;

  @IsString()
  @IsOptional()
  CLOUDINARY_FOLDER: string;

  @IsString()
  @IsOptional()
  CLOUDINARY_SECURE: string;
}

export default registerAs<CloudinaryConfigType>('cloudinary', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);
  return {
    cloudName: process.env.CLOUDINARY_CLOUD_NAME || '',
    apiKey: process.env.CLOUDINARY_API_KEY || '',
    apiSecret: process.env.CLOUDINARY_API_SECRET || '',
    folder: process.env.CLOUDINARY_FOLDER || 'assets',
    secure: process.env.CLOUDINARY_SECURE === 'true',
  };
});
