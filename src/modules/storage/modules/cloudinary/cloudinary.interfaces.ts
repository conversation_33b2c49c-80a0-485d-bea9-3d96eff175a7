import { Type } from '@nestjs/common';

/**
 * Configuration options for the CloudinaryModule
 */
export interface CloudinaryModuleOptions {
  /**
   * Cloudinary cloud name
   */
  cloudName: string;

  /**
   * Cloudinary API key
   */
  apiKey: string;

  /**
   * Cloudinary API secret
   */
  apiSecret: string;

  /**
   * Default folder for uploads
   * @default 'assets'
   */
  folder?: string;

  /**
   * Whether to use secure URLs
   * @default false
   */
  secure?: boolean;

  /**
   * Whether to register the module as global
   * @default false
   */
  isGlobal?: boolean;

  /**
   * Custom injection token for the module
   * Allows for multiple instances with different configurations
   */
  token?: string | symbol;
}

/**
 * Factory interface for creating CloudinaryModuleOptions
 */
export interface CloudinaryOptionsFactory {
  createCloudinaryOptions():
    | Promise<CloudinaryModuleOptions>
    | CloudinaryModuleOptions;
}

/**
 * Async options for configuring the CloudinaryModule
 */
export interface CloudinaryAsyncOptions {
  /**
   * Injection token
   */
  inject?: any[];

  /**
   * Whether to register the module as global
   * @default false
   */
  isGlobal?: boolean;

  /**
   * Factory function to create options
   */
  useFactory?: (
    ...args: any[]
  ) => Promise<CloudinaryModuleOptions> | CloudinaryModuleOptions;

  /**
   * Class to use for creating options
   */
  useClass?: Type<CloudinaryOptionsFactory>;

  /**
   * Existing provider to use for options
   */
  useExisting?: Type<CloudinaryOptionsFactory>;

  /**
   * Custom injection token for the module
   * Allows for multiple instances with different configurations
   */
  token?: string | symbol;

  /**
   * Optional imports needed for this module instance
   */
  imports?: any[];
}

/**
 * Default injection token for CloudinaryModuleOptions
 */
export const CLOUDINARY_OPTIONS = 'CLOUDINARY_OPTIONS';

/**
 * Creates a custom injection token for CloudinaryModuleOptions
 * @param name Optional name to create a unique token
 * @returns A unique injection token
 */
export function createCloudinaryOptionsToken(name?: string): string {
  return name ? `${CLOUDINARY_OPTIONS}_${name}` : CLOUDINARY_OPTIONS;
}
