import { Inject, Injectable, Logger, Optional } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as cloudinary from 'cloudinary';
import { Readable } from 'stream';
import { CloudinaryConfigType } from '@app/modules/storage/modules/cloudinary/cloudinary.config';
import {
  CLOUDINARY_OPTIONS,
  CloudinaryModuleOptions,
} from '@app/modules/storage/modules/cloudinary/cloudinary.interfaces';

// Types for CloudinaryService options
interface CloudinaryUploadOptions {
  /** Custom folder path (overrides default folder) */
  folder?: string;
  /** Custom public ID for the file */
  public_id?: string;
  /** Resource type: image, video, raw, auto */
  resource_type?: 'image' | 'video' | 'raw' | 'auto';
  /** File format to convert to */
  format?: string;
  /** Image quality (1-100 or 'auto') */
  quality?: number | 'auto';
  /** Tags to add to the file */
  tags?: string[];
  /** Context metadata */
  context?: Record<string, string>;
  /** Transformations to apply during upload */
  transformation?: Record<string, any>;
  /** Access mode: public or authenticated */
  access_mode?: 'public' | 'authenticated';
  /** Whether to use unique filename */
  use_filename?: boolean;
  /** Whether to overwrite existing files */
  overwrite?: boolean;
  /** Whether to invalidate CDN cache */
  invalidate?: boolean;
  /** Upload preset name */
  upload_preset?: string;
  /** Allowed image formats */
  allowed_formats?: string[];
  /** Async processing options */
  async?: boolean;
  /** Backup settings */
  backup?: boolean;
  /** Auto tagging confidence threshold */
  auto_tagging?: number;
  /** Categorization settings */
  categorization?: string;
  /** Color analysis settings */
  colors?: boolean;
  /** Detection settings for objects/faces */
  detection?: string;
  /** OCR settings */
  ocr?: string;
  /** Custom coordinates */
  custom_coordinates?: string;
  /** Face coordinates */
  face_coordinates?: string;
  /** Image metadata extraction */
  image_metadata?: boolean;
  /** Phash perceptual hash */
  phash?: boolean;
  /** Responsive breakpoints */
  responsive_breakpoints?: Array<{
    create_derived?: boolean;
    bytes_step?: number;
    min_width?: number;
    max_width?: number;
    max_images?: number;
    transformation?: Record<string, any>;
  }>;
}

interface CloudinaryArchiveOptions {
  /** Archive type: zip or tar */
  type?: 'zip' | 'tar';
  /** Archive mode: create or download */
  mode?: 'create' | 'download';
  /** Target format for files in archive */
  target_format?: string;
  /** Whether to flatten folder structure */
  flatten?: boolean;
  /** Whether to remove tags after archive creation */
  removeTags?: boolean;
}

interface CloudinarySpriteOptions {
  /** Transformation to apply to sprite */
  transformation?: Record<string, any>;
  /** Output format for sprite */
  format?: 'png' | 'jpg' | 'gif';
  /** Whether to remove tags after sprite generation */
  removeTags?: boolean;
}

@Injectable()
export class CloudinaryService {
  private readonly logger = new Logger(CloudinaryService.name);

  private readonly cloudinaryV2: typeof cloudinary.v2;
  private readonly folder: string;
  private readonly secure: boolean;
  private readonly cloudName: string;
  private readonly apiKey: string;
  private readonly apiSecret: string;

  constructor(
    private readonly configService: ConfigService<{
      cloudinary: CloudinaryConfigType;
    }>,
    @Optional()
    @Inject(CLOUDINARY_OPTIONS)
    private readonly options: CloudinaryModuleOptions,
  ) {
    if (this.options) {
      this.cloudName = this.options.cloudName;
      this.apiKey = this.options.apiKey;
      this.apiSecret = this.options.apiSecret;
      this.folder = this.options.folder || '';
      this.secure = this.options.secure || true;
    } else {
      const cloudName = this.configService.getOrThrow('cloudinary.cloudName', {
        infer: true,
      });
      const apiKey = this.configService.getOrThrow('cloudinary.apiKey', {
        infer: true,
      });
      const apiSecret = this.configService.getOrThrow('cloudinary.apiSecret', {
        infer: true,
      });
      const folder = this.configService.getOrThrow('cloudinary.folder', {
        infer: true,
      });
      const secure = this.configService.getOrThrow('cloudinary.secure', {
        infer: true,
      });

      this.cloudName = cloudName;
      this.apiKey = apiKey;
      this.apiSecret = apiSecret;
      this.folder = folder;
      this.secure = secure;
    }
    // Initialize Cloudinary
    this.cloudinaryV2 = cloudinary.v2;
    this.cloudinaryV2.config({
      cloud_name: this.cloudName,
      api_key: this.apiKey,
      api_secret: this.apiSecret,
      secure: this.secure,
    });
  }

  /**
   * Upload a file to Cloudinary
   * @param file - File buffer
   * @param filePath - Path to store the file
   * @param options - Upload options
   * @returns Upload result
   */
  async uploadFile(
    file: Buffer,
    filePath: string,
    options?: CloudinaryUploadOptions,
  ): Promise<{
    url: string;
    path: string;
    cdnUrl?: string;
    metadata?: Record<string, any>;
  }> {
    try {
      // Extract file name from path
      const fileName = filePath.split('/').pop();
      if (!fileName) {
        throw new Error('Invalid file path');
      }

      // Create a readable stream from the buffer
      const stream = new Readable();
      stream.push(file);
      stream.push(null); // End the stream

      // Create upload stream
      const uploadOptions: cloudinary.UploadApiOptions = {
        folder: this.folder,
        public_id: fileName.split('.')[0], // Remove extension
        resource_type: 'auto', // Auto-detect resource type
        ...options,
      };

      // Upload to Cloudinary
      const result = await new Promise<any>((resolve, reject) => {
        const uploadStream = this.cloudinaryV2.uploader.upload_stream(
          uploadOptions,
          (error, result) => {
            if (error) {
              // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors
              reject(error);
              return;
            }
            resolve(result);
          },
        );

        stream.pipe(uploadStream);
      });

      return {
        url: result.secure_url,
        path: `${this.folder}/${result.public_id}`,
        cdnUrl: result.secure_url,
        metadata: {
          publicId: result.public_id,
          format: result.format,
          resourceType: result.resource_type,
          width: result.width,
          height: result.height,
          size: result.bytes,
          version: result.version,
          assetId: result.asset_id,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error uploading file to Cloudinary: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Delete a file from Cloudinary
   * @param filePath - Path of the file to delete
   * @returns Deletion result
   */
  async deleteFile(filePath: string): Promise<boolean> {
    try {
      // Extract public ID from path
      const publicId = filePath.includes('/')
        ? filePath
        : `${this.folder}/${filePath}`;

      // Delete from Cloudinary
      const result = await this.cloudinaryV2.uploader.destroy(publicId);

      return result.result === 'ok';
    } catch (error) {
      this.logger.error(
        `Error deleting file from Cloudinary: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Get a signed URL for a file
   * @param filePath - Path of the file
   * @param expiresIn - Expiration time in seconds
   * @returns Signed URL
   */
  getSignedUrl(filePath: string, expiresIn: number = 3600): Promise<string> {
    try {
      // Extract public ID from path
      const publicId = filePath.includes('/')
        ? filePath
        : `${this.folder}/${filePath}`;

      // Generate signed URL
      const timestamp = Math.floor(Date.now() / 1000) + expiresIn;
      const signature = this.cloudinaryV2.utils.api_sign_request(
        { public_id: publicId, timestamp },
        this.apiSecret,
      );

      const url = this.cloudinaryV2.url(publicId, {
        secure: this.secure,
        sign_url: true,
        signature,
        timestamp,
      });
      return Promise.resolve(url);
    } catch (error) {
      this.logger.error(
        `Error generating signed URL: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get the public URL for a file
   * @param filePath - Path of the file
   * @returns Public URL
   */
  getPublicUrl(filePath: string): string {
    // Extract public ID from path
    const publicId = filePath.includes('/')
      ? filePath
      : `${this.folder}/${filePath}`;

    return this.cloudinaryV2.url(publicId, {
      secure: this.secure,
    });
  }

  /**
   * Get file details from Cloudinary
   * @param filePath - Path of the file
   * @returns File details
   */
  async getFileDetails(filePath: string): Promise<any> {
    try {
      const publicId = filePath.includes('/')
        ? filePath
        : `${this.folder}/${filePath}`;

      const result = await this.cloudinaryV2.api.resource(publicId);
      return result;
    } catch (error) {
      this.logger.error(
        `Error getting file details: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * List files in a folder
   * @param folderPath - Folder path (optional, defaults to configured folder)
   * @param options - List options
   * @returns List of files
   */
  async listFiles(
    folderPath?: string,
    options?: {
      maxResults?: number;
      nextCursor?: string;
      resourceType?: 'image' | 'video' | 'raw' | 'auto';
    },
  ): Promise<{
    resources: any[];
    nextCursor?: string;
  }> {
    try {
      const folder = folderPath || this.folder;
      const result = await this.cloudinaryV2.api.resources({
        type: 'upload',
        prefix: folder,
        max_results: options?.maxResults || 50,
        next_cursor: options?.nextCursor,
        resource_type: options?.resourceType || 'image',
      });

      return {
        resources: result.resources,
        nextCursor: result.next_cursor,
      };
    } catch (error) {
      this.logger.error(`Error listing files: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create a folder in Cloudinary
   * @param folderPath - Path of the folder to create
   * @returns Creation result
   */
  async createFolder(folderPath: string): Promise<boolean> {
    try {
      await this.cloudinaryV2.api.create_folder(folderPath);
      return true;
    } catch (error) {
      this.logger.error(`Error creating folder: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Delete a folder from Cloudinary
   * @param folderPath - Path of the folder to delete
   * @returns Deletion result
   */
  async deleteFolder(folderPath: string): Promise<boolean> {
    try {
      const result = await this.cloudinaryV2.api.delete_folder(folderPath);
      return result.deleted && result.deleted.length > 0;
    } catch (error) {
      this.logger.error(`Error deleting folder: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Generate a transformed image URL
   * @param filePath - Path of the file
   * @param transformations - Transformation options
   * @returns Transformed URL
   */
  getTransformedUrl(
    filePath: string,
    transformations: {
      width?: number;
      height?: number;
      crop?: 'scale' | 'fit' | 'fill' | 'crop' | 'thumb' | 'pad';
      quality?: number | 'auto';
      format?: 'jpg' | 'png' | 'webp' | 'avif' | 'auto';
      gravity?:
        | 'auto'
        | 'face'
        | 'center'
        | 'north'
        | 'south'
        | 'east'
        | 'west';
      effect?: string;
      overlay?: string;
      underlay?: string;
      angle?: number;
      radius?: number | 'max';
      border?: string;
      opacity?: number;
      dpr?: number;
    },
  ): string {
    const publicId = filePath.includes('/')
      ? filePath
      : `${this.folder}/${filePath}`;

    return this.cloudinaryV2.url(publicId, {
      secure: this.secure,
      ...transformations,
    });
  }

  /**
   * Upload multiple files to Cloudinary
   * @param files - Array of file buffers with paths
   * @param options - Upload options
   * @returns Array of upload results
   */
  async uploadMultipleFiles(
    files: Array<{ buffer: Buffer; path: string }>,
    options?: CloudinaryUploadOptions,
  ): Promise<
    Array<{
      url: string;
      path: string;
      cdnUrl?: string;
      metadata?: Record<string, any>;
    }>
  > {
    const uploadPromises = files.map((file) =>
      this.uploadFile(file.buffer, file.path, options),
    );

    try {
      return await Promise.all(uploadPromises);
    } catch (error) {
      this.logger.error(
        `Error uploading multiple files: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Delete multiple files from Cloudinary
   * @param filePaths - Array of file paths to delete
   * @returns Array of deletion results
   */
  async deleteMultipleFiles(filePaths: string[]): Promise<boolean[]> {
    const deletePromises = filePaths.map((path) => this.deleteFile(path));

    try {
      return await Promise.all(deletePromises);
    } catch (error) {
      this.logger.error(
        `Error deleting multiple files: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Search for files by tags or other criteria
   * @param query - Search query
   * @param options - Search options
   * @returns Search results
   */
  async searchFiles(
    query: string,
    options?: {
      maxResults?: number;
      nextCursor?: string;
      sortBy?: Array<{ field: string; direction: 'asc' | 'desc' }>;
    },
  ): Promise<{
    resources: any[];
    nextCursor?: string;
    totalCount?: number;
  }> {
    try {
      let searchQuery = this.cloudinaryV2.search
        .expression(query)
        .max_results(options?.maxResults || 50)
        .next_cursor(options?.nextCursor || '');

      // Apply sort criteria
      const sortBy = options?.sortBy || [
        { field: 'created_at', direction: 'desc' },
      ];
      sortBy.forEach((sort) => {
        searchQuery = searchQuery.sort_by(sort.field, sort.direction);
      });

      const result = await searchQuery.execute();

      return {
        resources: result.resources,
        nextCursor: result.next_cursor,
        totalCount: result.total_count,
      };
    } catch (error) {
      this.logger.error(`Error searching files: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add tags to a file
   * @param filePath - Path of the file
   * @param tags - Array of tags to add
   * @returns Update result
   */
  async addTags(filePath: string, tags: string[]): Promise<boolean> {
    try {
      const publicId = filePath.includes('/')
        ? filePath
        : `${this.folder}/${filePath}`;

      await this.cloudinaryV2.uploader.add_tag(tags.join(','), [publicId]);
      return true;
    } catch (error) {
      this.logger.error(`Error adding tags: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Remove tags from a file
   * @param filePath - Path of the file
   * @param tags - Array of tags to remove
   * @returns Update result
   */
  async removeTags(filePath: string, tags: string[]): Promise<boolean> {
    try {
      const publicId = filePath.includes('/')
        ? filePath
        : `${this.folder}/${filePath}`;

      await this.cloudinaryV2.uploader.remove_tag(tags.join(','), [publicId]);
      return true;
    } catch (error) {
      this.logger.error(`Error removing tags: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Update file metadata/context
   * @param filePath - Path of the file
   * @param context - Context data to update
   * @returns Update result
   */
  async updateContext(
    filePath: string,
    context: Record<string, string>,
  ): Promise<boolean> {
    try {
      const publicId = filePath.includes('/')
        ? filePath
        : `${this.folder}/${filePath}`;

      const contextString = Object.entries(context)
        .map(([key, value]) => `${key}=${value}`)
        .join('|');

      await this.cloudinaryV2.uploader.add_context(contextString, [publicId]);
      return true;
    } catch (error) {
      this.logger.error(
        `Error updating context: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Generate an archive (ZIP) of multiple files using uploader
   * @param filePaths - Array of file paths to include
   * @param options - Archive options
   * @returns Archive URL
   */
  async createArchive(
    filePaths: string[],
    options?: CloudinaryArchiveOptions,
  ): Promise<string> {
    try {
      const publicIds = filePaths.map((path) =>
        path.includes('/') ? path : `${this.folder}/${path}`,
      );

      // Use the uploader.create_archive method
      const result = await this.cloudinaryV2.uploader.create_archive({
        type: options?.type || 'zip',
        mode: options?.mode || 'create',
        target_format: options?.target_format,
        flatten_folders: options?.flatten || false,
        public_ids: publicIds,
      });

      return result.secure_url || result.url;
    } catch (error) {
      this.logger.error(
        `Error creating archive: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Create a ZIP file by tagging files and using generate_archive
   * @param filePaths - Array of file paths to include
   * @param archiveTag - Tag to use for archive generation
   * @param options - Archive options
   * @returns Archive URL
   */
  async createArchiveByTag(
    filePaths: string[],
    archiveTag: string,
    options?: CloudinaryArchiveOptions,
  ): Promise<string> {
    try {
      const publicIds = filePaths.map((path) =>
        path.includes('/') ? path : `${this.folder}/${path}`,
      );

      // Add tag to all files
      await this.cloudinaryV2.uploader.add_tag(archiveTag, publicIds);

      // Generate archive using the tag
      const result = await this.cloudinaryV2.uploader.create_archive({
        type: options?.type || 'zip',
        tag: archiveTag,
      });

      // Optionally remove the tag after archive generation
      if (options?.removeTags !== false) {
        await this.cloudinaryV2.uploader.remove_tag(archiveTag, publicIds);
      }

      return result.secure_url || result.url;
    } catch (error) {
      this.logger.error(
        `Error creating archive by tag: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get usage statistics
   * @returns Usage statistics
   */
  async getUsageStats(): Promise<any> {
    try {
      const result = await this.cloudinaryV2.api.usage();
      return result;
    } catch (error) {
      this.logger.error(
        `Error getting usage stats: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Rename a file
   * @param oldPath - Current file path
   * @param newPath - New file path
   * @returns Rename result
   */
  async renameFile(oldPath: string, newPath: string): Promise<boolean> {
    try {
      const oldPublicId = oldPath.includes('/')
        ? oldPath
        : `${this.folder}/${oldPath}`;
      const newPublicId = newPath.includes('/')
        ? newPath
        : `${this.folder}/${newPath}`;

      await this.cloudinaryV2.uploader.rename(oldPublicId, newPublicId);
      return true;
    } catch (error) {
      this.logger.error(`Error renaming file: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Generate a sprite from multiple images by tag
   * @param tag - Tag to identify images for sprite generation
   * @param options - Sprite options
   * @returns Sprite URL
   */
  async generateSpriteByTag(
    tag: string,
    options?: CloudinarySpriteOptions,
  ): Promise<string> {
    try {
      const result = await this.cloudinaryV2.uploader.generate_sprite(tag, {
        transformation: options?.transformation,
        format: options?.format || 'png',
      });

      return result.secure_url || result.url;
    } catch (error) {
      this.logger.error(
        `Error generating sprite: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Create a multi-image sprite by first tagging images then generating sprite
   * @param filePaths - Array of image paths
   * @param spriteTag - Tag to use for sprite generation
   * @param options - Sprite options
   * @returns Sprite URL
   */
  async createSpriteFromImages(
    filePaths: string[],
    spriteTag: string,
    options?: CloudinarySpriteOptions,
  ): Promise<string> {
    try {
      // First, add the sprite tag to all images
      const publicIds = filePaths.map((path) =>
        path.includes('/') ? path : `${this.folder}/${path}`,
      );

      // Add tag to all images
      await this.cloudinaryV2.uploader.add_tag(spriteTag, publicIds);

      // Generate sprite using the tag
      const result = await this.cloudinaryV2.uploader.generate_sprite(
        spriteTag,
        {
          transformation: options?.transformation,
          format: options?.format || 'png',
        },
      );

      // Optionally remove the tag after sprite generation
      if (options?.removeTags !== false) {
        await this.cloudinaryV2.uploader.remove_tag(spriteTag, publicIds);
      }

      return result.secure_url || result.url;
    } catch (error) {
      this.logger.error(
        `Error creating sprite from images: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
