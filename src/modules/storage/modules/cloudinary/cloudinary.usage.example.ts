/**
 * CloudinaryModule Usage Examples
 *
 * This file demonstrates various ways to configure and use the CloudinaryModule
 * in your NestJS applications.
 */

import { Module, Injectable, Inject } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  CloudinaryModule,
  CloudinaryService,
  CloudinaryModuleOptions,
  CloudinaryOptionsFactory,
} from './';

// Example 1: Basic static configuration
@Module({
  imports: [
    CloudinaryModule.forRoot({
      cloudName: 'my-cloud-name',
      apiKey: 'my-api-key',
      apiSecret: 'my-api-secret',
      folder: 'uploads',
      secure: true,
      isGlobal: true,
    }),
  ],
})
export class BasicCloudinaryModule {}

// Example 2: Async configuration with factory function
@Module({
  imports: [
    ConfigModule.forRoot(),
    CloudinaryModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        cloudName: configService.getOrThrow('CLOUDINARY_CLOUD_NAME'),
        apiKey: configService.getOrThrow('CLOUDINARY_API_KEY'),
        apiSecret: configService.getOrThrow('CLOUDINARY_API_SECRET'),
        folder: configService.get('CLOUDINARY_FOLDER', 'uploads'),
        secure: configService.get('CLOUDINARY_SECURE') === 'true',
        isGlobal: true,
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AsyncCloudinaryModule {}

// Example 3: Using configuration class
@Injectable()
class CloudinaryConfigService implements CloudinaryOptionsFactory {
  constructor(private configService: ConfigService) {}

  createCloudinaryOptions(): CloudinaryModuleOptions {
    return {
      cloudName: this.configService.getOrThrow('CLOUDINARY_CLOUD_NAME'),
      apiKey: this.configService.getOrThrow('CLOUDINARY_API_KEY'),
      apiSecret: this.configService.getOrThrow('CLOUDINARY_API_SECRET'),
      folder: this.configService.get('CLOUDINARY_FOLDER', 'uploads'),
      secure: this.configService.get('CLOUDINARY_SECURE') === 'true',
    };
  }
}

@Module({
  imports: [
    ConfigModule.forRoot(),
    CloudinaryModule.forRootAsync({
      imports: [ConfigModule],
      useClass: CloudinaryConfigService,
    }),
  ],
  providers: [CloudinaryConfigService],
})
export class ClassConfigCloudinaryModule {}

// Example 4: Multiple instances with different configurations
@Module({
  imports: [
    // Main cloudinary for general uploads
    CloudinaryModule.forRoot({
      cloudName: 'main-cloud',
      apiKey: 'main-api-key',
      apiSecret: 'main-api-secret',
      folder: 'general-uploads',
      secure: true,
    }),

    // Specific cloudinary for user avatars
    CloudinaryModule.forFeature('USER_AVATARS', {
      cloudName: 'user-cloud',
      apiKey: 'user-api-key',
      apiSecret: 'user-api-secret',
      folder: 'user-avatars',
      secure: true,
    }),

    // Specific cloudinary for product images
    CloudinaryModule.forFeature('PRODUCT_IMAGES', {
      cloudName: 'product-cloud',
      apiKey: 'product-api-key',
      apiSecret: 'product-api-secret',
      folder: 'product-images',
      secure: true,
    }),
  ],
})
export class MultiInstanceCloudinaryModule {}

// Example 5: Service usage with multiple instances
@Injectable()
export class FileUploadService {
  constructor(
    // Default cloudinary instance
    private readonly cloudinaryService: CloudinaryService,

    // User avatar specific instance
    @Inject('CloudinaryService_USER_AVATARS')
    private readonly avatarCloudinary: CloudinaryService,

    // Product image specific instance
    @Inject('CloudinaryService_PRODUCT_IMAGES')
    private readonly productCloudinary: CloudinaryService,
  ) {}

  async uploadGeneralFile(file: Buffer, filename: string) {
    return this.cloudinaryService.uploadFile(file, filename);
  }

  async uploadUserAvatar(userId: string, file: Buffer) {
    return this.avatarCloudinary.uploadFile(file, `${userId}-avatar.jpg`, {
      transformation: {
        width: 200,
        height: 200,
        crop: 'fill',
        gravity: 'face',
      },
    });
  }

  async uploadProductImage(productId: string, file: Buffer) {
    return this.productCloudinary.uploadFile(file, `product-${productId}.jpg`, {
      transformation: {
        width: 800,
        height: 600,
        crop: 'fit',
        quality: 'auto',
      },
    });
  }
}

// Example 6: Feature async configuration
@Module({
  imports: [
    ConfigModule.forRoot(),

    // Main cloudinary
    CloudinaryModule.forRoot({
      cloudName: 'main-cloud',
      apiKey: 'main-api-key',
      apiSecret: 'main-api-secret',
      folder: 'uploads',
    }),

    // Feature-specific with async config
    CloudinaryModule.forFeatureAsync('TEMP_UPLOADS', {
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        cloudName: configService.getOrThrow('CLOUDINARY_CLOUD_NAME'),
        apiKey: configService.getOrThrow('CLOUDINARY_API_KEY'),
        apiSecret: configService.getOrThrow('CLOUDINARY_API_SECRET'),
        folder: configService.get('TEMP_UPLOAD_FOLDER', 'temp'),
        secure: false, // Temp files don't need secure URLs
      }),
      inject: [ConfigService],
    }),
  ],
})
export class FeatureAsyncCloudinaryModule {}

// Example 7: Environment-based configuration
@Module({
  imports: [
    CloudinaryModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const isProduction = configService.get('NODE_ENV') === 'production';

        return {
          cloudName: configService.getOrThrow('CLOUDINARY_CLOUD_NAME'),
          apiKey: configService.getOrThrow('CLOUDINARY_API_KEY'),
          apiSecret: configService.getOrThrow('CLOUDINARY_API_SECRET'),
          folder: isProduction ? 'production' : 'development',
          secure: isProduction,
          isGlobal: true,
        };
      },
      inject: [ConfigService],
    }),
  ],
})
export class EnvironmentBasedCloudinaryModule {}

// Example 8: Custom token usage
@Module({
  imports: [
    CloudinaryModule.forRoot({
      cloudName: 'my-cloud',
      apiKey: 'my-api-key',
      apiSecret: 'my-api-secret',
      folder: 'custom-folder',
      token: 'CUSTOM_CLOUDINARY',
    }),
  ],
})
export class CustomTokenCloudinaryModule {}

@Injectable()
export class CustomTokenService {
  constructor(
    @Inject('CloudinaryService_CUSTOM_CLOUDINARY')
    private readonly customCloudinary: CloudinaryService,
  ) {}

  async uploadWithCustomToken(file: Buffer, filename: string) {
    return this.customCloudinary.uploadFile(file, filename);
  }
}
