# CloudinaryModule

A dynamic NestJS module for integrating Cloudinary cloud storage service into your applications. This module supports multiple configuration methods and allows for multiple instances with different configurations.

## Table of Contents

- [Installation](#installation)
- [Configuration](#configuration)
- [Usage Examples](#usage-examples)
- [Multiple Instances](#multiple-instances)
- [API Reference](#api-reference)
- [Best Practices](#best-practices)

## Installation

The CloudinaryModule is already included in this boilerplate. Make sure you have the required environment variables set up:

```bash
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
CLOUDINARY_FOLDER=uploads
CLOUDINARY_SECURE=true
```

## Configuration

### 1. Basic Static Configuration

```typescript
import { Module } from '@nestjs/common';
import { CloudinaryModule } from './modules/storage/modules/cloudinary';

@Module({
  imports: [
    CloudinaryModule.forRoot({
      cloudName: 'my-cloud-name',
      apiKey: 'my-api-key',
      apiSecret: 'my-api-secret',
      folder: 'uploads',
      secure: true,
      isGlobal: true, // Makes the module available globally
    }),
  ],
})
export class AppModule {}
```

### 2. Async Configuration with Factory

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CloudinaryModule } from './modules/storage/modules/cloudinary';

@Module({
  imports: [
    ConfigModule.forRoot(),
    CloudinaryModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        cloudName: configService.get('CLOUDINARY_CLOUD_NAME'),
        apiKey: configService.get('CLOUDINARY_API_KEY'),
        apiSecret: configService.get('CLOUDINARY_API_SECRET'),
        folder: configService.get('CLOUDINARY_FOLDER', 'uploads'),
        secure: configService.get('CLOUDINARY_SECURE') === 'true',
        isGlobal: true,
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

### 3. Configuration with Class

```typescript
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CloudinaryOptionsFactory, CloudinaryModuleOptions } from './modules/storage/modules/cloudinary';

@Injectable()
export class CloudinaryConfigService implements CloudinaryOptionsFactory {
  constructor(private configService: ConfigService) {}

  createCloudinaryOptions(): CloudinaryModuleOptions {
    return {
      cloudName: this.configService.get('CLOUDINARY_CLOUD_NAME'),
      apiKey: this.configService.get('CLOUDINARY_API_KEY'),
      apiSecret: this.configService.get('CLOUDINARY_API_SECRET'),
      folder: this.configService.get('CLOUDINARY_FOLDER', 'uploads'),
      secure: this.configService.get('CLOUDINARY_SECURE') === 'true',
    };
  }
}

@Module({
  imports: [
    CloudinaryModule.forRootAsync({
      imports: [ConfigModule],
      useClass: CloudinaryConfigService,
    }),
  ],
  providers: [CloudinaryConfigService],
})
export class AppModule {}
```

## Usage Examples

### Basic File Upload

```typescript
import { Injectable } from '@nestjs/common';
import { CloudinaryService } from './modules/storage/modules/cloudinary';

@Injectable()
export class FileService {
  constructor(private readonly cloudinaryService: CloudinaryService) {}

  async uploadImage(imageBuffer: Buffer, fileName: string) {
    return this.cloudinaryService.uploadFile(imageBuffer, fileName, {
      resource_type: 'image',
      folder: 'user-uploads',
      transformation: {
        width: 800,
        height: 600,
        crop: 'fit',
        quality: 'auto',
      },
    });
  }

  async uploadVideo(videoBuffer: Buffer, fileName: string) {
    return this.cloudinaryService.uploadFile(videoBuffer, fileName, {
      resource_type: 'video',
      folder: 'video-uploads',
    });
  }
}
```

## Multiple Instances

The CloudinaryModule supports multiple instances with different configurations, which is useful when you need different settings for different types of files.

### Setting up Multiple Instances

```typescript
@Module({
  imports: [
    // Main Cloudinary instance
    CloudinaryModule.forRoot({
      cloudName: 'main-cloud',
      folder: 'general',
      secure: true,
    }),
    
    // Avatar-specific instance
    CloudinaryModule.forFeature('USER_AVATARS', {
      folder: 'avatars',
      secure: true,
    }),
    
    // Product image instance
    CloudinaryModule.forFeature('PRODUCT_IMAGES', {
      folder: 'products',
      secure: true,
    }),
  ],
})
export class AppModule {}
```

### Using Multiple Instances

```typescript
import { Injectable, Inject } from '@nestjs/common';
import { CloudinaryService } from './modules/storage/modules/cloudinary';

@Injectable()
export class MultiInstanceService {
  constructor(
    // Default instance
    private readonly cloudinaryService: CloudinaryService,
    
    // Named instances
    @Inject('CloudinaryService_USER_AVATARS')
    private readonly avatarCloudinary: CloudinaryService,
    
    @Inject('CloudinaryService_PRODUCT_IMAGES')
    private readonly productCloudinary: CloudinaryService,
  ) {}

  async uploadUserAvatar(userId: string, imageBuffer: Buffer) {
    return this.avatarCloudinary.uploadFile(imageBuffer, `${userId}-avatar`, {
      transformation: {
        width: 200,
        height: 200,
        crop: 'fill',
        gravity: 'face',
      },
    });
  }

  async uploadProductImage(productId: string, imageBuffer: Buffer) {
    return this.productCloudinary.uploadFile(imageBuffer, `product-${productId}`, {
      transformation: {
        width: 1000,
        height: 800,
        crop: 'fit',
        quality: 'auto',
      },
    });
  }
}
```

### Async Multiple Instances

```typescript
@Module({
  imports: [
    // Main instance
    CloudinaryModule.forRoot(),
    
    // Async feature instance
    CloudinaryModule.forFeatureAsync('TEMP_STORAGE', {
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        folder: configService.get('TEMP_FOLDER', 'temp'),
        secure: false,
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

## API Reference

### CloudinaryModuleOptions

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `cloudName` | `string` | - | Cloudinary cloud name |
| `apiKey` | `string` | - | Cloudinary API key |
| `apiSecret` | `string` | - | Cloudinary API secret |
| `folder` | `string` | `'assets'` | Default upload folder |
| `secure` | `boolean` | `false` | Use secure HTTPS URLs |
| `isGlobal` | `boolean` | `false` | Make module globally available |
| `token` | `string \| symbol` | - | Custom injection token |

### CloudinaryModule Methods

#### `forRoot(options?: CloudinaryModuleOptions): DynamicModule`
Register the module with static configuration.

#### `forRootAsync(options: CloudinaryAsyncOptions): DynamicModule`
Register the module with async configuration.

#### `forFeature(token: string | symbol, options?: CloudinaryModuleOptions): DynamicModule`
Create a feature-specific instance with its own configuration.

#### `forFeatureAsync(token: string | symbol, options: CloudinaryAsyncOptions): DynamicModule`
Create a feature-specific instance with async configuration.

### CloudinaryService

The CloudinaryService provides methods for file operations:

- `uploadFile(file: Buffer, filePath: string, options?: CloudinaryUploadOptions)`
- `uploadStream(stream: Readable, filePath: string, options?: CloudinaryUploadOptions)`
- `deleteFile(publicId: string, options?: object)`
- `getFileInfo(publicId: string, options?: object)`
- `generateSignedUrl(publicId: string, options?: object)`

## Best Practices

### 1. Environment-based Configuration

```typescript
CloudinaryModule.forRootAsync({
  imports: [ConfigModule],
  useFactory: (configService: ConfigService) => {
    const isProduction = configService.get('NODE_ENV') === 'production';
    
    return {
      cloudName: configService.get('CLOUDINARY_CLOUD_NAME'),
      apiKey: configService.get('CLOUDINARY_API_KEY'),
      apiSecret: configService.get('CLOUDINARY_API_SECRET'),
      folder: isProduction ? 'production' : 'development',
      secure: isProduction,
      isGlobal: true,
    };
  },
  inject: [ConfigService],
})
```

### 2. Organize by File Types

Use different instances for different file types:

```typescript
// Separate instances for different content types
CloudinaryModule.forFeature('IMAGES', { folder: 'images' }),
CloudinaryModule.forFeature('VIDEOS', { folder: 'videos' }),
CloudinaryModule.forFeature('DOCUMENTS', { folder: 'documents' }),
```

### 3. Security Considerations

- Always use environment variables for credentials
- Enable secure URLs in production
- Use signed URLs for private content
- Implement proper access controls

### 4. Performance Optimization

- Use appropriate transformations during upload
- Leverage Cloudinary's auto-quality features
- Implement caching strategies for frequently accessed files

## Migration from Static Module

If you're migrating from the old static CloudinaryModule:

**Before:**
```typescript
@Module({
  imports: [CloudinaryModule],
})
export class AppModule {}
```

**After:**
```typescript
@Module({
  imports: [
    CloudinaryModule.forRoot({
      // Your configuration options
      isGlobal: true,
    }),
  ],
})
export class AppModule {}
```

The service injection remains the same:

```typescript
constructor(private readonly cloudinaryService: CloudinaryService) {}
``` 