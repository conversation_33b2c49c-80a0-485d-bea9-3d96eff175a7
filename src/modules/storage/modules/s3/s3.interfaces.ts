import { Type } from '@nestjs/common';

/**
 * Configuration options for the S3Module
 */
export interface S3ModuleOptions {
  /**
   * AWS Region
   * @default 'us-east-1'
   */
  region?: string;

  /**
   * S3 Bucket name
   * @default 'my-bucket'
   */
  bucket?: string;

  /**
   * AWS Access Key ID
   */
  accessKeyId?: string;

  /**
   * AWS Secret Access Key
   */
  secretAccessKey?: string;

  /**
   * Custom S3 endpoint (for S3-compatible services)
   */
  endpoint?: string;

  /**
   * CDN domain for public URLs (e.g., CloudFront)
   */
  cdnDomain?: string;

  /**
   * Whether to register the module as global
   * @default false
   */
  isGlobal?: boolean;

  /**
   * Custom injection token for the module
   * Allows for multiple instances with different configurations
   */
  token?: string | symbol;
}

/**
 * Factory interface for creating S3ModuleOptions
 */
export interface S3OptionsFactory {
  createS3Options(): Promise<S3ModuleOptions> | S3ModuleOptions;
}

/**
 * Async options for configuring the S3Module
 */
export interface S3AsyncOptions {
  /**
   * Injection token
   */
  inject?: any[];

  /**
   * Whether to register the module as global
   * @default false
   */
  isGlobal?: boolean;

  /**
   * Factory function to create options
   */
  useFactory?: (...args: any[]) => Promise<S3ModuleOptions> | S3ModuleOptions;

  /**
   * Class to use for creating options
   */
  useClass?: Type<S3OptionsFactory>;

  /**
   * Existing provider to use for options
   */
  useExisting?: Type<S3OptionsFactory>;

  /**
   * Custom injection token for the module
   * Allows for multiple instances with different configurations
   */
  token?: string | symbol;

  /**
   * Optional imports needed for this module instance
   */
  imports?: any[];
}

/**
 * Default injection token for S3ModuleOptions
 */
export const S3_OPTIONS = 'S3_OPTIONS';

/**
 * Creates a custom injection token for S3ModuleOptions
 * @param name Optional name to create a unique token
 * @returns A unique injection token
 */
export function createS3OptionsToken(name?: string): string {
  return name ? `${S3_OPTIONS}_${name}` : S3_OPTIONS;
}
