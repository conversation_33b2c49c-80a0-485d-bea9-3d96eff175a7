import { DynamicModule, Module, Provider } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { S3Service } from './s3.service';
import {
  S3_OPTIONS,
  S3ModuleOptions,
  S3AsyncOptions,
  S3OptionsFactory,
  createS3OptionsToken,
} from './s3.interfaces';
import s3Config from './s3.config';

/**
 * S3Module provides file storage capabilities using Amazon S3 or S3-compatible services.
 *
 * This module can be configured in multiple ways:
 *
 * 1. Static configuration using `forRoot()` method:
 *    ```typescript
 *    // Basic usage
 *    S3Module.forRoot({
 *      region: 'us-west-2',
 *      bucket: 'my-uploads-bucket',
 *      accessKeyId: 'your-access-key',
 *      secretAccessKey: 'your-secret-key',
 *      isGlobal: true
 *    })
 *
 *    // With custom token for multiple instances
 *    S3Module.forRoot({
 *      region: 'us-west-2',
 *      bucket: 'my-avatars-bucket',
 *      token: 'AVATAR_S3'
 *    })
 *    ```
 *
 * 2. Async configuration using `forRootAsync()` method:
 *    ```typescript
 *    // Using factory function
 *    S3Module.forRootAsync({
 *      imports: [ConfigModule],
 *      useFactory: (configService: ConfigService) => ({
 *        region: configService.get('AWS_REGION'),
 *        bucket: configService.get('AWS_S3_BUCKET'),
 *        accessKeyId: configService.get('AWS_ACCESS_KEY_ID'),
 *        secretAccessKey: configService.get('AWS_SECRET_ACCESS_KEY')
 *      }),
 *      inject: [ConfigService]
 *    })
 *
 *    // Using class
 *    S3Module.forRootAsync({
 *      useClass: S3ConfigService
 *    })
 *
 *    // Using existing provider
 *    S3Module.forRootAsync({
 *      useExisting: ConfigService
 *    })
 *    ```
 *
 * 3. Default configuration using environment variables:
 *    ```typescript
 *    // Uses environment variables or defaults
 *    S3Module
 *    ```
 *
 * 4. Feature-specific instances using `forFeature()` method:
 *    ```typescript
 *    // Create a specific instance with its own configuration
 *    S3Module.forFeature('AVATAR_S3', {
 *      bucket: 'avatars-bucket'
 *    })
 *    ```
 */
@Module({
  imports: [ConfigModule.forFeature(s3Config)],
  providers: [S3Service],
  exports: [S3Service],
})
export class S3Module {
  /**
   * Get a specific instance of S3Service using a custom token.
   *
   * This method allows you to create multiple instances of the S3Module
   * with different configurations. This is useful when you need to store different
   * types of files in different buckets or different S3 accounts.
   *
   * Example:
   * ```typescript
   * @Module({
   *   imports: [
   *     // Main storage for general files
   *     S3Module.forRoot({
   *       region: 'us-east-1',
   *       bucket: 'general-files'
   *     }),
   *     // Specific storage for avatars
   *     S3Module.forFeature('AVATAR_S3', {
   *       region: 'us-west-2',
   *       bucket: 'user-avatars'
   *     })
   *   ]
   * })
   * export class AppModule {}
   *
   * // In your service:
   * @Injectable()
   * export class UserService {
   *   constructor(
   *     private readonly storageService: S3Service,
   *     @Inject('S3Service_AVATAR_S3')
   *     private readonly avatarStorage: S3Service
   *   ) {}
   * }
   * ```
   *
   * @param token The token used to identify the specific instance
   * @param options Optional configuration options for this specific instance
   * @returns A dynamic module with a specific S3Service instance
   */
  static forFeature(
    token: string | symbol,
    options: S3ModuleOptions = {},
  ): DynamicModule {
    const tokenString =
      typeof token === 'symbol' ? token.description : token.toString();
    const optionsToken = createS3OptionsToken(tokenString);
    const serviceToken = `S3Service_${tokenString}`;

    // Create a provider for the options
    const optionsProvider = {
      provide: optionsToken,
      useValue: options,
    };

    // Create a provider for the specific instance
    const serviceProvider = {
      provide: serviceToken,
      useFactory: (options: S3ModuleOptions, configService: ConfigService) => {
        return new S3Service(configService, options);
      },
      inject: [optionsToken, ConfigService],
    };

    return {
      module: S3Module,
      providers: [optionsProvider, serviceProvider],
      exports: [serviceToken],
    };
  }

  /**
   * Get a specific instance of S3Service using a custom token with async options.
   *
   * This method allows you to create multiple instances of the S3Module
   * with different configurations loaded asynchronously. This is useful when you need
   * to store different types of files in different buckets and the configuration
   * needs to be loaded from a service or other async source.
   *
   * Example:
   * ```typescript
   * @Module({
   *   imports: [
   *     // Main storage for general files
   *     S3Module.forRoot(),
   *     // Specific storage for avatars with async config
   *     S3Module.forFeatureAsync('AVATAR_S3', {
   *       imports: [ConfigModule],
   *       useFactory: (configService: ConfigService) => ({
   *         region: configService.get('AVATAR_AWS_REGION'),
   *         bucket: configService.get('AVATAR_S3_BUCKET')
   *       }),
   *       inject: [ConfigService]
   *     })
   *   ]
   * })
   * export class AppModule {}
   *
   * // In your service:
   * @Injectable()
   * export class UserService {
   *   constructor(
   *     @Inject('S3Service_AVATAR_S3')
   *     private readonly avatarStorage: S3Service
   *   ) {}
   *
   *   async uploadAvatar(userId: string, file: Buffer) {
   *     return this.avatarStorage.upload({
   *       Key: `avatars/${userId}.jpg`,
   *       Body: file
   *     });
   *   }
   * }
   * ```
   *
   * @param token The token used to identify the specific instance
   * @param asyncOptions Async configuration options for this specific instance
   * @returns A dynamic module with a specific S3Service instance
   */
  static forFeatureAsync(
    token: string | symbol,
    asyncOptions: S3AsyncOptions,
  ): DynamicModule {
    const tokenString =
      typeof token === 'symbol' ? token.description : token.toString();
    const optionsToken = createS3OptionsToken(tokenString);
    const serviceToken = `S3Service_${tokenString}`;

    const imports = [ConfigModule.forFeature(s3Config)];
    if (asyncOptions.imports?.length) {
      imports.push(...asyncOptions.imports);
    }

    // Create async providers for the options
    const optionsProviders = this.createAsyncProviders(
      asyncOptions,
      optionsToken,
    );

    // Create a provider for the specific instance
    const serviceProvider = {
      provide: serviceToken,
      useFactory: (options: S3ModuleOptions, configService: ConfigService) => {
        return new S3Service(configService, options);
      },
      inject: [optionsToken, ConfigService],
    };

    return {
      module: S3Module,
      imports,
      providers: [...optionsProviders, serviceProvider],
      exports: [serviceToken],
    };
  }

  /**
   * Register the module with static options.
   *
   * This method allows you to configure the S3Module with static options
   * at the time of module import. It's useful when you know your configuration values
   * at application bootstrap time.
   *
   * Example:
   * ```typescript
   * @Module({
   *   imports: [
   *     S3Module.forRoot({
   *       region: 'us-east-1',
   *       bucket: 'my-app-uploads',
   *       accessKeyId: 'AKIA...',
   *       secretAccessKey: 'xxx...',
   *       isGlobal: true
   *     })
   *   ]
   * })
   * export class AppModule {}
   * ```
   *
   * @param options Configuration options for the S3Module
   * @returns A dynamic module configuration
   */
  static forRoot(options: S3ModuleOptions = {}): DynamicModule {
    // Use custom token if provided, otherwise use default token
    const token = options.token
      ? createS3OptionsToken(options.token.toString())
      : S3_OPTIONS;

    const optionsProvider: Provider = {
      provide: token,
      useValue: options,
    };

    // Create a custom service provider with the specific token
    const serviceProvider = {
      provide: S3Service,
      useFactory: (options: S3ModuleOptions, configService: ConfigService) => {
        return new S3Service(configService, options);
      },
      inject: [token, ConfigService],
    };

    // If a custom token is provided, create an additional named service provider
    const providers: Provider[] = [optionsProvider, serviceProvider];

    if (options.token) {
      const tokenString =
        typeof options.token === 'symbol'
          ? options.token.description
          : options.token.toString();

      const serviceToken = `S3Service_${tokenString}`;

      const namedServiceProvider = {
        provide: serviceToken,
        useFactory: (
          options: S3ModuleOptions,
          configService: ConfigService,
        ) => {
          return new S3Service(configService, options);
        },
        inject: [token, ConfigService],
      };

      providers.push(namedServiceProvider);
    }

    const exports: any[] = [S3Service];

    if (options.token) {
      const tokenString =
        typeof options.token === 'symbol'
          ? options.token.description
          : options.token.toString();
      exports.push(`S3Service_${tokenString}`);
    }

    return {
      global: options.isGlobal,
      module: S3Module,
      imports: [ConfigModule.forFeature(s3Config)],
      providers,
      exports,
    };
  }

  /**
   * Register the module with async options.
   *
   * This method allows you to configure the S3Module asynchronously,
   * which is useful when configuration values are not available at application bootstrap
   * time and need to be loaded from a configuration service, database, or other source.
   *
   * Example with factory function:
   * ```typescript
   * @Module({
   *   imports: [
   *     ConfigModule.forRoot(),
   *     S3Module.forRootAsync({
   *       imports: [ConfigModule],
   *       useFactory: (configService: ConfigService) => ({
   *         region: configService.get('AWS_REGION'),
   *         bucket: configService.get('AWS_S3_BUCKET'),
   *         accessKeyId: configService.get('AWS_ACCESS_KEY_ID'),
   *         secretAccessKey: configService.get('AWS_SECRET_ACCESS_KEY'),
   *         isGlobal: true
   *       }),
   *       inject: [ConfigService]
   *     })
   *   ]
   * })
   * export class AppModule {}
   * ```
   *
   * Example with class:
   * ```typescript
   * @Injectable()
   * class S3ConfigService implements S3OptionsFactory {
   *   constructor(private configService: ConfigService) {}
   *
   *   createS3Options(): S3ModuleOptions {
   *     return {
   *       region: this.configService.get('AWS_REGION'),
   *       bucket: this.configService.get('AWS_S3_BUCKET')
   *     };
   *   }
   * }
   *
   * @Module({
   *   imports: [
   *     S3Module.forRootAsync({
   *       imports: [ConfigModule],
   *       useClass: S3ConfigService
   *     })
   *   ]
   * })
   * export class AppModule {}
   * ```
   *
   * @param options Async configuration options for the S3Module
   * @returns A dynamic module configuration
   */
  static forRootAsync(options: S3AsyncOptions): DynamicModule {
    const imports = [ConfigModule.forFeature(s3Config)];

    if (options.imports?.length) {
      imports.push(...options.imports);
    }

    // Use custom token if provided, otherwise use default token
    const token = options.token
      ? createS3OptionsToken(options.token.toString())
      : S3_OPTIONS;

    const asyncProviders = this.createAsyncProviders(options, token);

    // Create a custom service provider with the specific token
    const serviceProvider = {
      provide: S3Service,
      useFactory: (options: S3ModuleOptions, configService: ConfigService) => {
        return new S3Service(configService, options);
      },
      inject: [token, ConfigService],
    };

    // Combine all providers
    const providers: Provider[] = [...asyncProviders, serviceProvider];

    // If a custom token is provided, create an additional named service provider
    if (options.token) {
      const tokenString =
        typeof options.token === 'symbol'
          ? options.token.description
          : options.token.toString();

      const serviceToken = `S3Service_${tokenString}`;

      const namedServiceProvider = {
        provide: serviceToken,
        useFactory: (
          options: S3ModuleOptions,
          configService: ConfigService,
        ) => {
          return new S3Service(configService, options);
        },
        inject: [token, ConfigService],
      };

      providers.push(namedServiceProvider);
    }

    const exports: any[] = [S3Service];

    if (options.token) {
      const tokenString =
        typeof options.token === 'symbol'
          ? options.token.description
          : options.token.toString();
      exports.push(`S3Service_${tokenString}`);
    }

    return {
      global: options.isGlobal,
      module: S3Module,
      imports,
      providers,
      exports,
    };
  }

  private static createAsyncProviders(
    options: S3AsyncOptions,
    token: string = S3_OPTIONS,
  ): Provider[] {
    if (options.useExisting || options.useFactory) {
      return [this.createAsyncOptionsProvider(options, token)];
    }

    // Make sure useClass is defined before using it
    if (!options.useClass) {
      throw new Error(
        'Invalid async options: useClass, useExisting, or useFactory must be provided',
      );
    }

    return [
      this.createAsyncOptionsProvider(options, token),
      {
        provide: options.useClass,
        useClass: options.useClass,
      },
    ];
  }

  private static createAsyncOptionsProvider(
    options: S3AsyncOptions,
    token: string = S3_OPTIONS,
  ): Provider {
    if (options.useFactory) {
      return {
        provide: token,
        useFactory: options.useFactory,
        inject: options.inject || [],
      };
    }

    // Make sure either useExisting or useClass is defined
    const injectToken = options.useExisting || options.useClass;
    if (!injectToken) {
      throw new Error(
        'Invalid async options: useClass, useExisting, or useFactory must be provided',
      );
    }

    return {
      provide: token,
      useFactory: async (optionsFactory: S3OptionsFactory) =>
        await optionsFactory.createS3Options(),
      inject: [injectToken],
    };
  }
}
