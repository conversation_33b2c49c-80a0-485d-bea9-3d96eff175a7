# S3 Storage Module

The S3 Storage Module provides file storage capabilities using Amazon S3 or S3-compatible services. It's designed as a dynamic NestJS module that can be configured in multiple ways to suit different application needs.

## Features

- Store files in Amazon S3 or S3-compatible services
- Support for multiple S3 buckets and accounts
- Support for multiple storage instances with different configurations
- Flexible configuration options (static, async, environment variables)
- Presigned URLs for secure file uploads and downloads
- File operations: upload, download, delete, copy, list, etc.
- Support for CloudFront CDN integration

## Installation

This module is part of the application and doesn't require separate installation. Make sure you have the AWS SDK dependencies installed:

```bash
npm install @aws-sdk/client-s3 @aws-sdk/s3-presigned-post @aws-sdk/s3-request-presigner
```

## Configuration Options

The module accepts the following configuration options:

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `region` | string | 'us-east-1' | AWS region where your S3 bucket is located |
| `bucket` | string | 'my-bucket' | S3 bucket name |
| `accessKeyId` | string | undefined | AWS Access Key ID |
| `secretAccessKey` | string | undefined | AWS Secret Access Key |
| `endpoint` | string | undefined | Custom S3 endpoint (for S3-compatible services) |
| `cdnDomain` | string | undefined | CDN domain for public URLs (e.g., CloudFront) |
| `isGlobal` | boolean | false | Whether to register the module as global |
| `token` | string \| symbol | undefined | Custom injection token for multiple instances |

## Usage

### Basic Usage

The simplest way to use the module is to import it without any configuration:

```typescript
import { Module } from '@nestjs/common';
import { S3Module } from './modules/storage/modules/s3';

@Module({
  imports: [S3Module],
})
export class AppModule {}
```

This will use environment variables or default values for configuration.

### Static Configuration

You can provide static configuration options using the `forRoot` method:

```typescript
import { Module } from '@nestjs/common';
import { S3Module } from './modules/storage/modules/s3';

@Module({
  imports: [
    S3Module.forRoot({
      region: 'us-west-2',
      bucket: 'my-app-uploads',
      accessKeyId: 'AKIA...',
      secretAccessKey: 'xxx...',
      isGlobal: true,
    }),
  ],
})
export class AppModule {}
```

### Async Configuration

For more complex scenarios where configuration values need to be loaded asynchronously:

#### Using Factory Function

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { S3Module } from './modules/storage/modules/s3';

@Module({
  imports: [
    ConfigModule.forRoot(),
    S3Module.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        region: configService.get('AWS_REGION'),
        bucket: configService.get('AWS_S3_BUCKET'),
        accessKeyId: configService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: configService.get('AWS_SECRET_ACCESS_KEY'),
        cdnDomain: configService.get('AWS_CLOUDFRONT_DOMAIN'),
        isGlobal: true,
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

#### Using Class

```typescript
import { Injectable, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { S3Module, S3OptionsFactory, S3ModuleOptions } from './modules/storage/modules/s3';

@Injectable()
class S3ConfigService implements S3OptionsFactory {
  constructor(private configService: ConfigService) {}
  
  createS3Options(): S3ModuleOptions {
    return {
      region: this.configService.get('AWS_REGION'),
      bucket: this.configService.get('AWS_S3_BUCKET'),
      accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
      secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
    };
  }
}

@Module({
  imports: [
    ConfigModule.forRoot(),
    S3Module.forRootAsync({
      imports: [ConfigModule],
      useClass: S3ConfigService,
    }),
  ],
})
export class AppModule {}
```

#### Using Existing Provider

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { S3Module } from './modules/storage/modules/s3';
import { StorageConfigService } from './storage-config.service';

@Module({
  imports: [
    ConfigModule.forRoot(),
    S3Module.forRootAsync({
      imports: [ConfigModule],
      useExisting: StorageConfigService,
    }),
  ],
  providers: [StorageConfigService],
})
export class AppModule {}
```

### Multiple Storage Instances

You can create multiple instances of the S3Module with different configurations for different buckets or accounts:

#### Using forRoot with Token

```typescript
import { Module } from '@nestjs/common';
import { S3Module } from './modules/storage/modules/s3';

@Module({
  imports: [
    // Default storage for general files
    S3Module.forRoot({
      region: 'us-east-1',
      bucket: 'general-files-bucket',
    }),
    // Specific storage for user avatars
    S3Module.forRoot({
      region: 'us-west-2',
      bucket: 'user-avatars-bucket',
      token: 'AVATAR_S3',
    }),
  ],
})
export class AppModule {}
```

#### Using forFeature

```typescript
import { Module } from '@nestjs/common';
import { S3Module } from './modules/storage/modules/s3';

@Module({
  imports: [
    // Default storage
    S3Module.forRoot({
      region: 'us-east-1',
      bucket: 'main-bucket',
    }),
    // Specific storage for backups
    S3Module.forFeature('BACKUP_S3', {
      region: 'us-west-2',
      bucket: 'backup-bucket',
      endpoint: 'https://s3.us-west-2.amazonaws.com',
    }),
  ],
})
export class AppModule {}
```

#### Using forFeatureAsync

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { S3Module } from './modules/storage/modules/s3';

@Module({
  imports: [
    ConfigModule.forRoot(),
    // Default storage
    S3Module.forRoot(),
    // Specific storage for media files with async config
    S3Module.forFeatureAsync('MEDIA_S3', {
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        region: configService.get('MEDIA_AWS_REGION'),
        bucket: configService.get('MEDIA_S3_BUCKET'),
        cdnDomain: configService.get('MEDIA_CLOUDFRONT_DOMAIN'),
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

### S3-Compatible Services

You can use the module with S3-compatible services like MinIO, DigitalOcean Spaces, etc.:

```typescript
import { Module } from '@nestjs/common';
import { S3Module } from './modules/storage/modules/s3';

@Module({
  imports: [
    // DigitalOcean Spaces example
    S3Module.forRoot({
      region: 'nyc3',
      bucket: 'my-space',
      endpoint: 'https://nyc3.digitaloceanspaces.com',
      accessKeyId: 'your-spaces-key',
      secretAccessKey: 'your-spaces-secret',
    }),
    
    // MinIO example
    S3Module.forFeature('MINIO_STORAGE', {
      region: 'us-east-1',
      bucket: 'minio-bucket',
      endpoint: 'http://localhost:9000',
      accessKeyId: 'minioadmin',
      secretAccessKey: 'minioadmin',
    }),
  ],
})
export class AppModule {}
```

## Injecting the Service

### Default Instance

```typescript
import { Injectable } from '@nestjs/common';
import { S3Service } from './modules/storage/modules/s3';

@Injectable()
export class AppService {
  constructor(private readonly s3Service: S3Service) {}

  async uploadFile(file: Buffer, key: string) {
    return this.s3Service.upload({
      Key: key,
      Body: file,
      ContentType: 'image/jpeg',
    });
  }
}
```

### Named Instance

```typescript
import { Inject, Injectable } from '@nestjs/common';
import { S3Service } from './modules/storage/modules/s3';

@Injectable()
export class UserService {
  constructor(
    // Default S3 service
    private readonly s3Service: S3Service,
    // Avatar-specific S3 service
    @Inject('S3Service_AVATAR_S3')
    private readonly avatarS3: S3Service,
  ) {}

  async uploadAvatar(userId: string, file: Buffer) {
    return this.avatarS3.upload({
      Key: `avatars/${userId}.jpg`,
      Body: file,
      ContentType: 'image/jpeg',
    });
  }

  async uploadDocument(userId: string, file: Buffer, filename: string) {
    return this.s3Service.upload({
      Key: `documents/${userId}/${filename}`,
      Body: file,
    });
  }
}
```

## API Reference

### S3Service

The `S3Service` provides the following methods:

#### `upload(payload: UploadPayload): Promise<PutObjectCommandOutput>`
Upload a file to S3.
```typescript
const result = await s3Service.upload({
  Key: 'path/to/file.jpg',
  Body: fileBuffer,
  ContentType: 'image/jpeg',
  Bucket: 'custom-bucket', // Optional, uses default bucket if not provided
});
```

#### `download(payload: DownloadPayload): Promise<GetObjectCommandOutput>`
Download a file from S3.
```typescript
const result = await s3Service.download({
  Key: 'path/to/file.jpg',
  Bucket: 'custom-bucket', // Optional
});
```

#### `getMetadata(payload: MetadataPayload): Promise<HeadObjectCommandOutput>`
Get file metadata without downloading the file.
```typescript
const metadata = await s3Service.getMetadata({
  Key: 'path/to/file.jpg',
});
```

#### `getSignedUrl(payload: SignedUrlPayload): Promise<string>`
Generate a presigned URL for secure file access.
```typescript
const url = await s3Service.getSignedUrl({
  Key: 'path/to/file.jpg',
  expiresIn: 3600, // 1 hour
});
```

#### `createPresignedPost(payload: PresignedPostPayload): Promise<PresignedPost>`
Create a presigned POST for direct browser uploads.
```typescript
const presignedPost = await s3Service.createPresignedPost({
  Key: 'uploads/${filename}',
  conditions: [
    ['content-length-range', 0, 1024 * 1024 * 10], // 10MB max
  ],
  expiresIn: 300, // 5 minutes
});
```

#### `deleteFile(payload: DeletePayload): Promise<DeleteObjectCommandOutput>`
Delete a file from S3.
```typescript
await s3Service.deleteFile({
  Key: 'path/to/file.jpg',
});
```

## Environment Variables

When using the default configuration, the module will look for the following environment variables:

- `AWS_REGION`: AWS region (default: 'us-east-1')
- `AWS_S3_BUCKET` or `AWS_S3_BUCKET_NAME`: S3 bucket name (default: 'my-bucket')
- `AWS_ACCESS_KEY_ID`: AWS Access Key ID
- `AWS_SECRET_ACCESS_KEY`: AWS Secret Access Key
- `AWS_S3_ENDPOINT`: Custom S3 endpoint (optional)
- `AWS_CLOUDFRONT_DOMAIN`: CloudFront domain for CDN (optional)

## Error Handling

The S3Service will throw AWS SDK errors. It's recommended to handle these errors appropriately:

```typescript
import { Injectable, Logger } from '@nestjs/common';
import { S3Service } from './modules/storage/modules/s3';

@Injectable()
export class FileService {
  private readonly logger = new Logger(FileService.name);

  constructor(private readonly s3Service: S3Service) {}

  async uploadFile(file: Buffer, key: string) {
    try {
      const result = await this.s3Service.upload({
        Key: key,
        Body: file,
      });
      
      this.logger.log(`File uploaded successfully: ${key}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to upload file: ${key}`, error);
      throw error;
    }
  }
}
```

## Best Practices

### 1. Use Environment Variables for Credentials
Never hardcode AWS credentials in your source code. Use environment variables or AWS IAM roles.

### 2. Use Presigned URLs for Client-Side Uploads
For security and performance, use presigned URLs for direct client-to-S3 uploads:

```typescript
// Generate presigned POST for frontend
const presignedPost = await s3Service.createPresignedPost({
  Key: `uploads/${userId}/${filename}`,
  conditions: [
    ['content-length-range', 0, 1024 * 1024 * 5], // 5MB max
    ['starts-with', '$Content-Type', 'image/'],
  ],
  expiresIn: 300,
});

// Return to frontend for direct upload
return presignedPost;
```

### 3. Use Multiple Instances for Different Use Cases
Create separate S3 instances for different types of content:

```typescript
@Module({
  imports: [
    // Public assets
    S3Module.forRoot({
      bucket: 'public-assets',
      token: 'PUBLIC_S3',
    }),
    // Private user data
    S3Module.forFeature('PRIVATE_S3', {
      bucket: 'private-user-data',
    }),
  ],
})
export class AppModule {}
```

### 4. Implement Proper Error Handling
Always implement proper error handling and logging for S3 operations.

### 5. Use CloudFront for Better Performance
Configure CloudFront CDN for better performance and caching:

```typescript
S3Module.forRoot({
  bucket: 'my-bucket',
  cdnDomain: 'https://d123456789.cloudfront.net',
});
``` 