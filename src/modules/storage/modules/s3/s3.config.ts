import { registerAs } from '@nestjs/config';
import { IsOptional, IsString } from 'class-validator';
import validateConfig from '@app/common/config/validate-config';

export type S3ConfigType = {
  region: string;
  bucket: string;
  accessKeyId?: string;
  secretAccessKey?: string;
  endpoint?: string;
  cdnDomain?: string;
};

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  AWS_REGION: string;

  @IsString()
  @IsOptional()
  AWS_S3_BUCKET: string;

  @IsString()
  @IsOptional()
  AWS_ACCESS_KEY_ID: string;

  @IsString()
  @IsOptional()
  AWS_SECRET_ACCESS_KEY: string;

  @IsString()
  @IsOptional()
  AWS_S3_ENDPOINT: string;

  @IsString()
  @IsOptional()
  AWS_CLOUDFRONT_DOMAIN: string;
}

export default registerAs<S3ConfigType>('s3', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);
  return {
    region: process.env.AWS_REGION || 'us-east-1',
    bucket: process.env.AWS_S3_BUCKET || 'my-bucket',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    endpoint: process.env.AWS_S3_ENDPOINT, // Optional, for custom endpoints
    cdnDomain: process.env.AWS_CLOUDFRONT_DOMAIN, // Optional, for CloudFront
  };
});
