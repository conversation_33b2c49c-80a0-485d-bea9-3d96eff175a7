import {
  GetObjectCommand,
  GetObjectCommandOutput,
  HeadObjectCommand,
  HeadObjectCommandOutput,
  PutObjectCommand,
  PutObjectCommandOutput,
  S3Client,
} from '@aws-sdk/client-s3';
import { createPresignedPost, PresignedPost } from '@aws-sdk/s3-presigned-post';
import { Conditions } from '@aws-sdk/s3-presigned-post/dist-types/types';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Inject, Injectable, Optional } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3_OPTIONS, S3ModuleOptions } from './s3.interfaces';
import { S3ConfigType } from '@app/modules/storage/modules/s3/s3.config';

@Injectable()
export class S3Service {
  private readonly s3: S3Client;
  private readonly DEFAULT_BUCKET_NAME: string;

  constructor(
    private readonly configService: ConfigService<{
      s3: S3ConfigType;
    }>,
    @Optional()
    @Inject(S3_OPTIONS)
    private readonly options: S3ModuleOptions,
  ) {
    let region: string;
    let accessKeyId: string | undefined;
    let secretAccessKey: string | undefined;
    let endpoint: string | undefined;

    if (this.options) {
      region = this.options.region || 'us-east-1';
      accessKeyId = this.options.accessKeyId;
      secretAccessKey = this.options.secretAccessKey;
      endpoint = this.options.endpoint;
      this.DEFAULT_BUCKET_NAME = this.options.bucket || 'my-bucket';
    } else {
      region = this.configService.getOrThrow('s3.region', {
        infer: true,
      });
      accessKeyId = this.configService.getOrThrow('s3.accessKeyId', {
        infer: true,
      });
      secretAccessKey = this.configService.getOrThrow('s3.secretAccessKey', {
        infer: true,
      });
      endpoint = this.configService.getOrThrow('s3.endpoint', {
        infer: true,
      });
      this.DEFAULT_BUCKET_NAME = this.configService.getOrThrow('s3.bucket', {
        infer: true,
      });
    }

    const s3Config: any = {
      region,
    };

    if (accessKeyId && secretAccessKey) {
      s3Config.credentials = {
        accessKeyId,
        secretAccessKey,
      };
    }

    if (endpoint) {
      s3Config.endpoint = endpoint;
    }

    this.s3 = new S3Client(s3Config);
  }

  async upload(payload: {
    Bucket?: string;
    Key: string;
    Body: any;
    ContentType?: string;
  }): Promise<PutObjectCommandOutput> {
    const uploadResult = await this.s3.send(
      new PutObjectCommand({
        Bucket: payload.Bucket ?? this.DEFAULT_BUCKET_NAME,
        Key: payload.Key,
        Body: payload.Body,
        ContentType: payload.ContentType,
      }),
    );
    return uploadResult;
  }

  async createPresignedPost(payload: {
    bucket?: string;
    key: string;
    expire?: number;
    acl?: string;
    contentLengthRange?: [number, number];
  }): Promise<PresignedPost> {
    const conditions: Conditions[] = [];
    if (payload.contentLengthRange) {
      conditions.push([
        'content-length-range',
        payload.contentLengthRange[0],
        payload.contentLengthRange[1],
      ]);
    }

    if (!payload.key) {
      throw new Error('Key is required for presigned post');
    }

    // Ensure key is properly formatted and not undefined
    const key = payload.key.trim();

    // Add content type condition if provided
    const fields: Record<string, string> = {};
    if (payload.acl) {
      fields.acl = payload.acl;
    }

    const response = await createPresignedPost(this.s3, {
      Bucket: payload.bucket ?? this.DEFAULT_BUCKET_NAME,
      Key: key,
      Conditions: conditions,
      Expires: payload.expire,
      Fields: fields,
    });
    return response;
  }

  async getSignedUrl(payload: {
    bucket?: string;
    key?: string;
    contentType?: string;
  }): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: payload.bucket ?? this.DEFAULT_BUCKET_NAME,
      Key: payload.key,
    });
    const presignedUrl = await getSignedUrl(this.s3, command, {
      expiresIn: 60 * 5,
    });
    return presignedUrl;
  }

  async getObjectUrl(params: { bucket?: string; key: string }) {
    const signedUrl = await this.getSignedUrl({
      bucket: params.bucket ?? this.DEFAULT_BUCKET_NAME,
      key: params.key,
    });
    const s3Url = new URL(signedUrl);
    return s3Url.origin + s3Url.pathname;
  }

  async getObject(params: {
    bucket?: string;
    key: string;
  }): Promise<GetObjectCommandOutput> {
    const command = new GetObjectCommand({
      Bucket: params.bucket ?? this.DEFAULT_BUCKET_NAME,
      Key: params.key,
    });
    const response = await this.s3.send(command);

    return response;
  }

  async getObjectInfo(params: {
    bucket?: string;
    key: string;
  }): Promise<HeadObjectCommandOutput> {
    const command = new HeadObjectCommand({
      Bucket: params.bucket ?? this.DEFAULT_BUCKET_NAME,
      Key: params.key,
    });
    const response = await this.s3.send(command);
    return response;
  }

  getKeyFromUrl(url: string): string {
    const s3Url = new URL(url);
    const key = s3Url.pathname;
    if (!key) {
      throw new Error('Key is required for presigned post');
    }
    // remove leading slash
    return key.slice(1).split('/').map(decodeURIComponent).join('/');
  }

  getBucketFromUrl(url: string): string {
    const s3Url = new URL(url);
    return s3Url.hostname.split('.')[0];
  }
}
