import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { StorageModule } from './storage.module';
import { StorageProvider } from './enums/storage-provider.enum';
import {
  StorageAsyncOptions,
  StorageModuleOptions,
  StorageOptionsFactory,
} from '@app/modules/storage/storage.interface';

/**
 * Example usage of the dynamic StorageModule
 */

// ============================================================================
// Example 1: Basic usage with default environment configuration
// ============================================================================
@Module({
  imports: [
    // Uses all default environment configuration from storage.config.ts
    StorageModule.forRoot(),
  ],
})
export class BasicAppModule {}

// ============================================================================
// Example 2: Enable only specific storage providers
// ============================================================================
@Module({
  imports: [
    StorageModule.forRoot({
      enabledProviders: [StorageProvider.S3, StorageProvider.LOCAL],
      isGlobal: true,
    }),
  ],
})
export class SelectiveProvidersAppModule {}

// ============================================================================
// Example 3: Override specific configuration
// ============================================================================
@Module({
  imports: [
    StorageModule.forRoot({
      provider: StorageProvider.S3,
      enabledProviders: [StorageProvider.S3],
      config: {
        s3: {
          region: 'us-west-2',
          bucket: 'my-custom-bucket',
          cdnDomain: 'https://cdn.example.com',
        },
      },
    }),
  ],
})
export class CustomConfigAppModule {}

// ============================================================================
// Example 4: Async configuration with factory function
// ============================================================================
@Module({
  imports: [
    ConfigModule.forRoot(),
    StorageModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        provider: configService.get('STORAGE_PROVIDER') as StorageProvider,
        enabledProviders: [StorageProvider.S3, StorageProvider.CLOUDINARY],
        config: {
          s3: {
            region: configService.get('AWS_REGION') || 'us-east-1',
            bucket: configService.get('AWS_S3_BUCKET') || 'default-bucket',
            accessKeyId: configService.get('AWS_ACCESS_KEY_ID'),
            secretAccessKey: configService.get('AWS_SECRET_ACCESS_KEY'),
          },
          cloudinary: {
            cloudName: configService.get('CLOUDINARY_CLOUD_NAME'),
            apiKey: configService.get('CLOUDINARY_API_KEY'),
            apiSecret: configService.get('CLOUDINARY_API_SECRET'),
            folder: 'uploads',
            secure: true,
          },
        },
        isGlobal: true,
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AsyncFactoryAppModule {}

// ============================================================================
// Example 5: Async configuration with class
// ============================================================================
export class StorageConfigService implements StorageOptionsFactory {
  constructor(private readonly configService: ConfigService) {}

  createStorageOptions(): StorageModuleOptions {
    const environment = this.configService.get('NODE_ENV');

    if (environment === 'production') {
      return {
        provider: StorageProvider.S3,
        enabledProviders: [StorageProvider.S3],
        config: {
          s3: {
            region: this.configService.get('AWS_REGION') || 'us-east-1',
            bucket: this.configService.get('AWS_S3_BUCKET') || 'default-bucket',
            cdnDomain: this.configService.get('AWS_CLOUDFRONT_DOMAIN'),
          },
        },
      };
    } else {
      return {
        provider: StorageProvider.LOCAL,
        enabledProviders: [StorageProvider.LOCAL],
        config: {
          local: {
            uploadDir: 'temp/uploads',
            baseUrl: 'http://localhost:3000',
          },
        },
      };
    }
  }
}

@Module({
  imports: [
    ConfigModule.forRoot(),
    StorageModule.forRootAsync({
      imports: [ConfigModule],
      useClass: StorageConfigService,
      isGlobal: true,
    }),
  ],
})
export class AsyncClassAppModule {}

// ============================================================================
// Example 6: Using in a service
// ============================================================================
import { Injectable } from '@nestjs/common';
import { StorageFactoryService } from './services/storage-factory.service';
import { LocalStorageProvider } from './providers/local-storage.provider';
import { S3StorageProvider } from './providers/s3-storage.provider';

@Injectable()
export class FileUploadService {
  constructor(
    private readonly storageFactory: StorageFactoryService,
    private readonly localStorage: LocalStorageProvider,
    private readonly s3Storage: S3StorageProvider,
  ) {}

  async uploadFile(file: Buffer, filename: string): Promise<string> {
    // Use the configured storage provider
    const storage = this.storageFactory.getStorageProvider();
    const result = await storage.uploadFile(file, filename);
    return result.url;
  }

  async uploadToSpecificProvider(
    file: Buffer,
    filename: string,
    provider: 'local' | 's3',
  ): Promise<string> {
    // Use a specific storage provider directly
    const storage = provider === 'local' ? this.localStorage : this.s3Storage;
    const result = await storage.uploadFile(file, filename);
    return result.url;
  }
}

// ============================================================================
// Example 7: Environment-based configuration
// ============================================================================
@Module({
  imports: [
    ConfigModule.forRoot(),
    StorageModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const isDevelopment = configService.get('NODE_ENV') === 'development';
        const isProduction = configService.get('NODE_ENV') === 'production';

        if (isDevelopment) {
          return {
            provider: StorageProvider.LOCAL,
            enabledProviders: [StorageProvider.LOCAL],
            config: {
              local: {
                uploadDir: 'dev-uploads',
                baseUrl: 'http://localhost:3000',
              },
            },
          };
        }

        if (isProduction) {
          return {
            provider: StorageProvider.S3,
            enabledProviders: [StorageProvider.S3, StorageProvider.CLOUDINARY],
            config: {
              s3: {
                region: configService.getOrThrow('AWS_REGION'),
                bucket: configService.getOrThrow('AWS_S3_BUCKET'),
                cdnDomain: configService.get('AWS_CLOUDFRONT_DOMAIN'),
              },
            },
          };
        }

        // Default configuration
        return {
          enabledProviders: [StorageProvider.LOCAL],
        };
      },
      inject: [ConfigService],
    }),
  ],
})
export class EnvironmentBasedAppModule {}

// ============================================================================
// Example 8: Multiple storage instances (if needed)
// ============================================================================
@Module({
  imports: [
    // Main storage
    StorageModule.forRoot({
      enabledProviders: [StorageProvider.S3],
      token: 'MAIN_STORAGE',
    }),
    // Archive storage with different configuration
    StorageModule.forRootAsync({
      useFactory: () => ({
        enabledProviders: [StorageProvider.S3],
        config: {
          s3: {
            region: 'us-east-1',
            bucket: 'archive-bucket',
          },
        },
      }),
      token: 'ARCHIVE_STORAGE',
    }),
  ],
})
export class MultipleStorageAppModule {}
