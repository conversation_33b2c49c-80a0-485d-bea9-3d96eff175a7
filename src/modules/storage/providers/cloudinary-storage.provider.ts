import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IStorageProvider } from '../interfaces/storage-provider.interface';
import { CloudinaryService } from '../modules/cloudinary/cloudinary.service';
import { StorageConfigType } from '@app/modules/storage/storage.config';

@Injectable()
export class CloudinaryStorageProvider implements IStorageProvider {
  private readonly logger = new Logger(CloudinaryStorageProvider.name);

  constructor(
    private readonly configService: ConfigService<{
      storage: StorageConfigType;
    }>,
    private readonly cloudinaryService: CloudinaryService,
  ) {}

  /**
   * Upload a file to Cloudinary using CloudinaryService
   * @param file - File buffer
   * @param filePath - Path to store the file
   * @param options - Upload options
   * @returns Upload result
   */
  async uploadFile(
    file: Buffer,
    filePath: string,
    options?: Record<string, any>,
  ): Promise<{
    url: string;
    path: string;
    cdnUrl?: string;
    metadata?: Record<string, any>;
  }> {
    try {
      return await this.cloudinaryService.uploadFile(file, filePath, options);
    } catch (error) {
      this.logger.error(
        `Error uploading file via CloudinaryService: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Delete a file from Cloudinary using CloudinaryService
   * @param filePath - Path of the file to delete
   * @returns Deletion result
   */
  async deleteFile(filePath: string): Promise<boolean> {
    try {
      return await this.cloudinaryService.deleteFile(filePath);
    } catch (error) {
      this.logger.error(
        `Error deleting file via CloudinaryService: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Get a signed URL for a file using CloudinaryService
   * @param filePath - Path of the file
   * @param expiresIn - Expiration time in seconds
   * @returns Signed URL
   */
  getSignedUrl(filePath: string, expiresIn: number = 3600): Promise<string> {
    try {
      return this.cloudinaryService.getSignedUrl(filePath, expiresIn);
    } catch (error) {
      this.logger.error(
        `Error generating signed URL via CloudinaryService: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get the public URL for a file using CloudinaryService
   * @param filePath - Path of the file
   * @returns Public URL
   */
  getPublicUrl(filePath: string): string {
    return this.cloudinaryService.getPublicUrl(filePath);
  }
}
