import validateConfig from '@app/common/config/validate-config';
import { registerAs } from '@nestjs/config';
import {
  IsNotEmpty,
  IsNumberString,
  IsOptional,
  IsString,
} from 'class-validator';
import ms from 'ms';

export enum CacheStoreEnum {
  MEMORY = 'memory',
  REDIS = 'redis',
}

export type CacheConfigType = {
  ttl: ms.StringValue;
  lru: number;
  stores: CacheStoreEnum[];
  redis?: {
    host: string;
    port: number;
    username?: string;
    password?: string;
    database: number;
  };
};

class EnvironmentVariablesValidator {
  @IsNumberString()
  @IsNotEmpty()
  @IsOptional()
  CACHE_TTL: string;

  @IsNumberString()
  @IsNotEmpty()
  @IsOptional()
  CACHE_LRU: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  CACHE_STORE: string;

  @IsString()
  @IsOptional()
  CACHE_REDIS_HOST: string;

  @IsString()
  @IsOptional()
  CACHE_REDIS_PORT: string;

  @IsString()
  @IsOptional()
  CACHE_REDIS_USERNAME: string;

  @IsString()
  @IsOptional()
  CACHE_REDIS_PASSWORD: string;

  @IsNumberString()
  @IsOptional()
  CACHE_REDIS_DATABASE: string;
}

export default registerAs('cache', (): CacheConfigType => {
  validateConfig(process.env, EnvironmentVariablesValidator);
  return {
    ttl: (process.env.CACHE_TTL as ms.StringValue) || '10m',
    lru: parseInt(process.env.CACHE_LRU || '1000'), // 1000 items default LRU
    stores: process.env.CACHE_STORE
      ? process.env.CACHE_STORE.split(',').map(
          (store) => store as CacheStoreEnum,
        )
      : [CacheStoreEnum.MEMORY],
    redis: process.env.CACHE_REDIS_HOST
      ? {
          host: process.env.CACHE_REDIS_HOST,
          port: parseInt(process.env.CACHE_REDIS_PORT || '6379'),
          username: process.env.CACHE_REDIS_USERNAME,
          password: process.env.CACHE_REDIS_PASSWORD,
          database: parseInt(process.env.CACHE_REDIS_DATABASE || '0'),
        }
      : undefined,
  };
});
