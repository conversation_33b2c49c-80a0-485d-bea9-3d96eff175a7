import { TwoFactorService } from '@app/modules/auth/services/two-factor.service';
import { JwtPayloadType } from '@app/modules/auth/strategies/types/jwt-payload.type';
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';

export const SKIP_TWO_FACTOR_KEY = 'skipTwoFactor';
export const SkipTwoFactor = () => Reflector.createDecorator<boolean>();

@Injectable()
export class TwoFactorGuard implements CanActivate {
  constructor(
    private readonly twoFactorService: TwoFactorService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if two-factor authentication should be skipped for this route
    const skipTwoFactor = this.reflector.getAllAndOverride<boolean>(
      SKIP_TWO_FACTOR_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (skipTwoFactor) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user: JwtPayloadType = request.user;

    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }

    // Check if user has two-factor authentication enabled
    const requiresTwoFactor = await this.twoFactorService.requiresTwoFactorAuth(
      user.sub,
    );

    if (!requiresTwoFactor) {
      // User doesn't have 2FA enabled, allow access
      return true;
    }

    // Check if the user has already verified 2FA in this session
    // This could be implemented by checking a session flag or JWT claim
    // For now, we'll require 2FA verification for each request
    // In a real implementation, you might want to store 2FA verification status in the session

    // If user has 2FA enabled but hasn't verified it, deny access
    throw new UnauthorizedException(
      'Two-factor authentication verification required',
    );
  }
}
