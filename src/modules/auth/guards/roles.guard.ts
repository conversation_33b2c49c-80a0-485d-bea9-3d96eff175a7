import { ROLES_KEY } from '@app/modules/auth/decorators/roles.decorator';
import { UserRoleEnum } from '@app/modules/user/enums/user-role.enum';
import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<UserRoleEnum[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()],
    );

    // If no roles are required, allow access
    if (!requiredRoles) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Check if user exists in request
    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }

    // Validate that user has a valid role
    if (!user.role || !Object.values(UserRoleEnum).includes(user.role)) {
      throw new ForbiddenException('Invalid user role');
    }

    // Check if user has required role
    const hasRole = requiredRoles.some((role) => user.role === role);
    if (!hasRole) {
      throw new ForbiddenException(
        `User role ${user.role} is not authorized. Required roles: ${requiredRoles.join(', ')}`,
      );
    }

    return true;
  }
}
