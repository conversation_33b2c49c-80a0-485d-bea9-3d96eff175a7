import { HttpErrorException } from '@app/common/exception';
import { UserLoginEntity } from '@app/modules/auth/entities/user-login.entity';
import { USER_ERROR_CODES } from '@app/modules/user/user.error-code';
import { Injectable } from '@nestjs/common';

import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class UserLoginService {
  constructor(
    @InjectRepository(UserLoginEntity)
    private readonly userLoginRepo: Repository<UserLoginEntity>,
  ) {}

  getByUserId(userId: string, relations?: string[]) {
    return this.userLoginRepo.findOne({
      where: { userId },
      relations,
    });
  }

  async getByUserIdOrThrow(userId: string, relations?: string[]) {
    const userLogin = await this.getByUserId(userId, relations);
    if (!userLogin) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
    }
    return userLogin;
  }

  getByUsername(username: string, relations?: string[]) {
    return this.userLoginRepo.findOne({
      where: { username },
      relations,
    });
  }

  async getByUsernameOrThrow(username: string, relations?: string[]) {
    const userLogin = await this.getByUsername(username, relations);
    if (!userLogin) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
    }
    return userLogin;
  }

  update(id: string, data: Partial<UserLoginEntity>) {
    return this.userLoginRepo.update(id, data);
  }

  create(userId: string, ipAddress: string, userAgent: string) {
    return this.userLoginRepo.save({
      userId,
      ipAddress,
      userAgent,
    });
  }

  getByResetPasswordToken(token: string) {
    return this.userLoginRepo.findOne({
      where: { resetPasswordToken: token },
    });
  }
}
