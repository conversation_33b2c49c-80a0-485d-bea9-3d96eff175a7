import { toDto } from '@app/common/dto/to-dto';
import { HttpErrorException } from '@app/common/exception/http-error.exception';
import { base64Decode, base64Encode } from '@app/common/utils/crypto';
import { AuthConfigType } from '@app/modules/auth/auth.config';
import { AUTH_ERROR_CODES } from '@app/modules/auth/auth.error-code';
import { LoginResponseDto } from '@app/modules/auth/dto/auth-response.dto';
import { SessionEntity } from '@app/modules/auth/entities/session.entity';
import { SocialEntity } from '@app/modules/auth/entities/social.entity';
import { SocialProviderEnum } from '@app/modules/auth/enums/social-provider.enum';
import { AuthService } from '@app/modules/auth/services/auth.service';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { USER_ERROR_CODES } from '@app/modules/user/user.error-code';
import { Injectable, NotFoundException } from '@nestjs/common';
import { randomStringGenerator } from '@nestjs/common/utils/random-string-generator.util';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import * as crypto from 'crypto';
import * as ms from 'ms';
import { Repository } from 'typeorm';

@Injectable()
export class SocialService {
  constructor(
    private readonly configService: ConfigService<{
      auth: AuthConfigType;
    }>,
    @InjectRepository(SocialEntity)
    private readonly socialRepo: Repository<SocialEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(SessionEntity)
    private readonly sessionRepo: Repository<SessionEntity>,
    private readonly authService: AuthService,
    private readonly jwtService: JwtService,
  ) {}

  async saveSocialUser(payload: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    picture: string;
    accessToken: string;
    refreshToken: string;
    provider: SocialProviderEnum;
  }) {
    const socialProvider = this.socialRepo.create({
      provider: payload.provider,
      providerId: payload.id,
      email: payload.email,
      firstName: payload.firstName,
      lastName: payload.lastName,
      picture: payload.picture,
      accessToken: payload.accessToken,
      refreshToken: payload.refreshToken,
    });
    await this.socialRepo.upsert(socialProvider, {
      conflictPaths: ['providerId', 'provider'],
    });

    let user = await this.userRepo.findOne({
      where: { email: payload.email },
    });
    if (!user) {
      user = this.userRepo.create({
        email: payload.email,
        firstName: payload.firstName,
        lastName: payload.lastName,
      });
      await this.userRepo.save(user);
    }
    const session = await this.authService.createSession(user.id, '', '', {
      sub: user.id,
      email: user.email,
    });
    const authorizationCode = base64Encode(session.id);

    return {
      authorizationCode,
    };
  }

  async loginWithCode(
    authorizationCode: string,
    ipAddress: string,
    userAgent: string,
  ) {
    const sessionId = base64Decode(authorizationCode);
    console.log(sessionId);
    const session = await this.sessionRepo.findOne({
      where: { id: sessionId },
    });
    if (!session) {
      throw new NotFoundException('Session not found');
    }
    const sessionExpires = this.configService.getOrThrow(
      'auth.sessionExpires',
      {
        infer: true,
      },
    );

    if (session.isExpired(ms(sessionExpires))) {
      throw new HttpErrorException(AUTH_ERROR_CODES.SESSION_EXPIRED);
    }
    if (!session.userId) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
    }
    const user = await this.userRepo.findOne({
      where: { id: session.userId },
    });
    if (!user) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
    }
    session.ipAddress = ipAddress;
    session.userAgent = userAgent;
    await this.sessionRepo.save(session);

    const hash = crypto
      .createHash('sha256')
      .update(randomStringGenerator())
      .digest('hex');

    const { accessToken, refreshToken, tokenExpires } =
      await this.authService.getTokensData({
        userId: user.id,
        sessionId: session.id,
        hash: hash,
      });
    return toDto(
      {
        refreshToken,
        accessToken,
        tokenExpires,
        user,
      },
      LoginResponseDto,
    );
  }
}
