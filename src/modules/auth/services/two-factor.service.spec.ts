import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { TwoFactorService } from './two-factor.service';
import { UserService } from '@app/modules/user/user.service';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { HttpErrorException } from '@app/common/exception/http-error.exception';

describe('TwoFactorService', () => {
  let service: TwoFactorService;
  let userService: jest.Mocked<UserService>;
  let configService: jest.Mocked<ConfigService>;

  const mockUser: UserEntity = {
    id: '123',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    isEmailVerified: true,
    isActive: true,
    isTwoFactorEnabled: false,
    twoFactorSecret: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockUserService = {
      findById: jest.fn(),
      update: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn().mockReturnValue('TestApp'),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TwoFactorService,
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<TwoFactorService>(TwoFactorService);
    userService = module.get(UserService);
    configService = module.get(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateTwoFactorSecret', () => {
    it('should generate a secret for a user without 2FA enabled', async () => {
      userService.findById.mockResolvedValue(mockUser);
      userService.update.mockResolvedValue({ ...mockUser, twoFactorSecret: 'secret' });

      const result = await service.generateTwoFactorSecret('123');

      expect(result).toHaveProperty('secret');
      expect(result).toHaveProperty('otpAuthUrl');
      expect(result).toHaveProperty('qrCodeDataUrl');
      expect(result.qrCodeDataUrl).toMatch(/^data:image\/png;base64,/);
    });

    it('should throw error if user not found', async () => {
      userService.findById.mockResolvedValue(null);

      await expect(service.generateTwoFactorSecret('123')).rejects.toThrow(HttpErrorException);
    });

    it('should throw error if 2FA is already enabled', async () => {
      userService.findById.mockResolvedValue({ ...mockUser, isTwoFactorEnabled: true });

      await expect(service.generateTwoFactorSecret('123')).rejects.toThrow(HttpErrorException);
    });
  });

  describe('getTwoFactorStatus', () => {
    it('should return 2FA status', async () => {
      userService.findById.mockResolvedValue(mockUser);

      const result = await service.getTwoFactorStatus('123');

      expect(result).toEqual({ isTwoFactorEnabled: false });
    });
  });

  describe('requiresTwoFactorAuth', () => {
    it('should return false for user without 2FA', async () => {
      userService.findById.mockResolvedValue(mockUser);

      const result = await service.requiresTwoFactorAuth('123');

      expect(result).toBe(false);
    });

    it('should return true for user with 2FA enabled', async () => {
      userService.findById.mockResolvedValue({ ...mockUser, isTwoFactorEnabled: true });

      const result = await service.requiresTwoFactorAuth('123');

      expect(result).toBe(true);
    });

    it('should return false if user not found', async () => {
      userService.findById.mockResolvedValue(null);

      const result = await service.requiresTwoFactorAuth('123');

      expect(result).toBe(false);
    });
  });
});
