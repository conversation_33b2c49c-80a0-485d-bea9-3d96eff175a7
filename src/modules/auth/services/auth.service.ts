import { toDto } from '@app/common/dto/to-dto';
import { HttpErrorException } from '@app/common/exception/http-error.exception';
import { generateOtp } from '@app/common/utils/string';
import { AuthConfigType } from '@app/modules/auth/auth.config';
import { AUTH_ERROR_CODES } from '@app/modules/auth/auth.error-code';
import { LoginResponseDto, TwoFactorLoginResponseDto } from '@app/modules/auth/dto/auth-response.dto';
import { CompleteTwoFactorLoginDto } from '@app/modules/auth/dto/two-factor.dto';
import { ChangePasswordInput } from '@app/modules/auth/dto/change-password.dto';
import {
  LoginPasswordLessEmailInput,
  VerifyOtpLoginPasswordLessEmailInput,
} from '@app/modules/auth/dto/login-password-less-email.dto';
import { RegisterDto } from '@app/modules/auth/dto/register.dto';
import {
  RequestResetPasswordByEmailLinkInput,
  ResetPasswordByEmailLinkInput,
} from '@app/modules/auth/dto/reset-password-by-email-link.dto';
import {
  RequestResetPasswordByEmailOtpInput,
  ResetPasswordByEmailOtpInput,
} from '@app/modules/auth/dto/reset-password-email-otp.dto';
import { OtpEntity } from '@app/modules/auth/entities/otp.entity';
import { UserLoginEntity } from '@app/modules/auth/entities/user-login.entity';
import { OtpTypeEnum } from '@app/modules/auth/enums/otp-type.enum';
import { UserLoginService } from '@app/modules/auth/services/user-login.service';
import { MailerService } from '@app/modules/mailer/mailer.service';
import { UserService } from '@app/modules/user/user.service';
import { RefreshTokenDto } from '@modules/auth/dto/refresh-token.dto';
import { SessionEntity } from '@modules/auth/entities/session.entity';
import { JwtRefreshPayloadType } from '@modules/auth/strategies/types/jwt-refresh-payload.type';
import {
  ConflictException,
  Injectable,
  InternalServerErrorException,
  UnauthorizedException,
} from '@nestjs/common';
import { randomStringGenerator } from '@nestjs/common/utils/random-string-generator.util';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import * as ms from 'ms';
import * as speakeasy from 'speakeasy';
import { Repository } from 'typeorm';

@Injectable()
export class AuthService {
  constructor(
    private readonly configService: ConfigService<{
      auth: AuthConfigType;
    }>,
    private readonly jwtService: JwtService,
    private readonly userService: UserService,
    private readonly mailerService: MailerService,
    private readonly userLoginService: UserLoginService,
    @InjectRepository(SessionEntity)
    private readonly sessionRepo: Repository<SessionEntity>,
    @InjectRepository(OtpEntity)
    private readonly otpRepo: Repository<OtpEntity>,
  ) {}

  async register(
    registerDto: RegisterDto,
    userAgent: string,
    ipAddress: string,
  ) {
    const user = await this.userService.create({
      ...registerDto,
    });
    const userLogin = await this.userLoginService.create(
      user.id,
      ipAddress,
      userAgent,
    );

    // Log in the user automatically after registration
    const loginResult = await this.login(userLogin, userAgent, ipAddress);

    return loginResult;
  }

  async validateUser(username: string, password: string) {
    const userLogin = await this.userLoginService.getByUsername(username);
    if (
      userLogin &&
      userLogin.password &&
      (await bcrypt.compare(password, userLogin.password))
    ) {
      return userLogin;
    }
    return null;
  }

  async loginByEmail(email: string, password: string) {
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }
    const userLogin = await this.userLoginService.getByUserId(user.id);
    if (!userLogin) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }
    if (
      userLogin &&
      userLogin.password &&
      (await bcrypt.compare(password, userLogin.password))
    ) {
      return userLogin;
    }
    return null;
  }

  async login(
    userLogin: UserLoginEntity,
    userAgent: string,
    ipAddress: string,
  ): Promise<LoginResponseDto | TwoFactorLoginResponseDto> {
    const user = await this.userService.findById(userLogin.userId);
    if (!user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }

    // Check if user has two-factor authentication enabled
    if (user.isTwoFactorEnabled) {
      // Generate a temporary token for two-factor authentication
      const tempToken = this.jwtService.sign(
        {
          sub: userLogin.userId,
          username: userLogin.username,
          type: 'temp_2fa',
          userAgent,
          ipAddress
        },
        { expiresIn: '10m' } // Temporary token expires in 10 minutes
      );

      return toDto(
        {
          tempToken,
          requiresTwoFactor: true,
          message: 'Please provide your two-factor authentication code',
        },
        TwoFactorLoginResponseDto,
      );
    }

    // Proceed with normal login if 2FA is not enabled
    return this.completeLogin(userLogin, userAgent, ipAddress, user);
  }

  async completeTwoFactorLogin(
    dto: CompleteTwoFactorLoginDto,
    userAgent: string,
    ipAddress: string,
  ): Promise<LoginResponseDto> {
    // Verify the temporary token
    let tempPayload;
    try {
      tempPayload = this.jwtService.verify(dto.tempToken);
    } catch (error) {
      throw new HttpErrorException({
        code: 'INVALID_TEMP_TOKEN',
        statusCode: 401,
        message: 'Invalid or expired temporary token',
      });
    }

    if (tempPayload.type !== 'temp_2fa') {
      throw new HttpErrorException({
        code: 'INVALID_TOKEN_TYPE',
        statusCode: 401,
        message: 'Invalid token type',
      });
    }

    // Get user and verify 2FA code
    const user = await this.userService.findById(tempPayload.sub);
    if (!user || !user.isTwoFactorEnabled || !user.twoFactorSecret) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }

    // Verify TOTP code
    const isValid = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token: dto.totpCode,
      window: 2,
    });

    if (!isValid) {
      throw new HttpErrorException({
        code: 'INVALID_2FA_CODE',
        statusCode: 401,
        message: 'Invalid two-factor authentication code',
      });
    }

    // Get user login entity
    const userLogin = await this.userLoginService.getByUsernameOrThrow(tempPayload.username);

    // Complete the login process
    return this.completeLogin(userLogin, userAgent, ipAddress, user);
  }

  private async completeLogin(
    userLogin: UserLoginEntity,
    userAgent: string,
    ipAddress: string,
    user: any,
  ): Promise<LoginResponseDto> {
    const payload = { username: userLogin.username, sub: userLogin.userId };
    const session = new SessionEntity();
    session.userId = userLogin.userId;
    session.hash = this.jwtService.sign(payload);
    session.userAgent = userAgent;
    session.ipAddress = ipAddress;

    await this.sessionRepo.save(session);

    const hash = crypto
      .createHash('sha256')
      .update(randomStringGenerator())
      .digest('hex');

    const { accessToken, refreshToken, tokenExpires } =
      await this.getTokensData({
        userId: userLogin.userId,
        sessionId: session.id,
        hash,
      });

    return toDto(
      {
        refreshToken,
        accessToken,
        tokenExpires,
        user,
        requiresTwoFactor: false,
      },
      LoginResponseDto,
    );
  }

  async loginPasswordLessEmail(input: LoginPasswordLessEmailInput) {
    const user = await this.userService.findByEmail(input.email);
    if (!user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }
    const otp = generateOtp();

    const otpEntity = new OtpEntity();
    otpEntity.userId = user.id;
    otpEntity.otp = otp;
    otpEntity.type = OtpTypeEnum.LOGIN;
    await this.otpRepo.save(otpEntity);

    const loginPasswordLessEmailConfig = this.configService.getOrThrow(
      'auth.loginPasswordLessEmail',
      {
        infer: true,
      },
    );
    const otpExpires = ms(loginPasswordLessEmailConfig.expires) / ms('1m');

    const template = loginPasswordLessEmailConfig.templateEmail;
    if (template) {
      await this.mailerService.sendMailWithTemplate({
        to: user.email,
        template,
        subject: loginPasswordLessEmailConfig.subjectEmail,
        context: {
          otp,
          otpExpiresInMinutes: otpExpires,
        },
      });
    } else {
      await this.mailerService.sendMail({
        to: user.email,
        subject: loginPasswordLessEmailConfig.subjectEmail,
        text: `Hello ${user.firstName},
  
  Your login OTP is ${otp}.
  
  This OTP will expire in ${otpExpires} minutes.
  
  If you did not request this OTP, please ignore this email.
  
  Best regards,
  `,
      });
    }
  }

  async verifyOtpLoginPasswordLessEmail(
    input: VerifyOtpLoginPasswordLessEmailInput,
    userAgent: string,
    ipAddress: string,
  ) {
    const { email, otp } = input;
    const otpEntity = await this.otpRepo.findOne({
      where: { otp, type: OtpTypeEnum.LOGIN },
      relations: ['user', 'userLogin'],
    });

    if (!otpEntity || !otpEntity.userLogin || !otpEntity.user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_OTP);
    }

    if (otpEntity?.user?.email !== email) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_OTP);
    }

    const resetPasswordConfig = this.configService.getOrThrow(
      'auth.resetPassword',
      {
        infer: true,
      },
    );
    const expires = ms(resetPasswordConfig.expires);

    if (otpEntity.createdAt.getTime() + expires < Date.now()) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_OTP);
    }

    await this.otpRepo.delete(otpEntity.id);

    const userLogin = otpEntity.userLogin;

    return this.login(userLogin, userAgent, ipAddress);
  }

  async requestResetPasswordByEmailLink(
    input: RequestResetPasswordByEmailLinkInput,
  ) {
    const { email } = input;
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }

    const resetPasswordConfig = this.configService.getOrThrow(
      'auth.resetPassword',
      {
        infer: true,
      },
    );
    if (!resetPasswordConfig.resetPasswordUrl) {
      throw new InternalServerErrorException('Reset password URL is not set');
    }
    const resetPasswordToken = randomStringGenerator();

    await this.userLoginService.update(user.id, {
      resetPasswordToken,
      resetPasswordTokenAt: new Date(),
    });

    const resetPasswordUrl = `${resetPasswordConfig.resetPasswordUrl}?token=${resetPasswordToken}`;

    return resetPasswordUrl;
  }

  async resetPasswordByEmailLink(input: ResetPasswordByEmailLinkInput) {
    const { token } = input;
    const userLogin =
      await this.userLoginService.getByResetPasswordToken(token);
    if (!userLogin) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
    }
    const resetPasswordConfig = this.configService.getOrThrow(
      'auth.resetPassword',
      {
        infer: true,
      },
    );
    const expires = ms(resetPasswordConfig.expires);
    if (
      !userLogin.resetPasswordTokenAt ||
      userLogin.resetPasswordTokenAt.getTime() + expires < Date.now()
    ) {
      throw new UnauthorizedException('Reset password link expired');
    }
    const passwordHash = await bcrypt.hash(input.newPassword, 10);
    await this.userLoginService.update(userLogin.id, {
      resetPasswordToken: null,
      resetPasswordTokenAt: null,
      password: passwordHash,
      passwordChangedAt: new Date(),
    });
  }

  async requestResetPasswordByEmailOtp(
    input: RequestResetPasswordByEmailOtpInput,
  ) {
    const { email } = input;
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }
    const otp = generateOtp();

    const otpEntity = new OtpEntity();
    otpEntity.userId = user.id;
    otpEntity.otp = otp;
    otpEntity.type = OtpTypeEnum.RESET_PASSWORD;
    await this.otpRepo.save(otpEntity);

    const resetPasswordConfig = this.configService.getOrThrow(
      'auth.resetPassword',
      {
        infer: true,
      },
    );
    const otpExpires = ms(resetPasswordConfig.expires) / ms('1m');

    const template = resetPasswordConfig.templateEmailOtp;
    if (template) {
      await this.mailerService.sendMailWithTemplate({
        to: user.email,
        template,
        subject: resetPasswordConfig.subjectEmailOtp,
        context: {
          otp,
          otpExpiresInMinutes: otpExpires,
        },
      });
    } else {
      await this.mailerService.sendMail({
        to: user.email,
        subject: resetPasswordConfig.subjectEmailOtp,
        text: `Hello ${user.firstName},
  
  Your reset password OTP is ${otp}.
  
  This OTP will expire in ${otpExpires} minutes.
  
  If you did not request this OTP, please ignore this email.
  
  Best regards,
  `,
      });
    }
  }

  async resetPasswordByEmailOtp(input: ResetPasswordByEmailOtpInput) {
    const { email, otp, newPassword } = input;
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const otpEntity = await this.otpRepo.findOne({
      where: { otp, type: OtpTypeEnum.RESET_PASSWORD },
      relations: ['user'],
    });

    if (!otpEntity || !otpEntity.user) {
      throw new UnauthorizedException('Invalid OTP');
    }

    if (otpEntity?.user?.email !== email) {
      throw new UnauthorizedException('Invalid OTP');
    }

    const resetPasswordConfig = this.configService.getOrThrow(
      'auth.resetPassword',
      {
        infer: true,
      },
    );
    const expires = ms(resetPasswordConfig.expires);

    if (otpEntity.createdAt.getTime() + expires < Date.now()) {
      throw new HttpErrorException(AUTH_ERROR_CODES.INVALID_OTP);
    }

    await this.otpRepo.delete(otpEntity.id);

    const passwordHash = await bcrypt.hash(newPassword, 10);

    await this.userLoginService.update(user.id, {
      password: passwordHash,
      passwordChangedAt: new Date(),
    });
  }

  async createSession(
    userId: string | null,
    userAgent: string,
    ipAddress: string,
    payload: any,
  ) {
    const session = new SessionEntity();
    session.userId = userId;
    session.userAgent = userAgent;
    session.ipAddress = ipAddress;
    session.hash = this.jwtService.sign(payload);
    await this.sessionRepo.save(session);
    return session;
  }

  public async getTokensData(data: {
    userId: string;
    sessionId: string;
    hash: string;
  }) {
    const tokenExpiresIn = this.configService.getOrThrow('auth.expires', {
      infer: true,
    });

    // Use a simple method to set token expiration
    const tokenExpires = Date.now() + 3600000; // Default to 1 hour

    const [accessToken, refreshToken] = await Promise.all([
      await this.jwtService.signAsync(
        {
          sub: data.userId,
          sessionId: data.sessionId,
        },
        {
          secret: this.configService.getOrThrow('auth.secret', { infer: true }),
          expiresIn: tokenExpiresIn,
        },
      ),
      await this.jwtService.signAsync(
        {
          sessionId: data.sessionId,
          hash: data.hash,
        },
        {
          secret: this.configService.getOrThrow('auth.refreshSecret', {
            infer: true,
          }),
          expiresIn: this.configService.getOrThrow('auth.refreshExpires', {
            infer: true,
          }),
        },
      ),
    ]);

    return {
      accessToken,
      refreshToken,
      tokenExpires,
    };
  }

  async refreshToken(input: RefreshTokenDto) {
    let jwtRefreshPayload: JwtRefreshPayloadType;
    try {
      jwtRefreshPayload = this.jwtService.verify(input.refreshToken, {
        secret: this.configService.getOrThrow('auth.refreshSecret', {
          infer: true,
        }),
      });
    } catch (error: any) {
      console.log(error);
      throw new UnauthorizedException();
    }

    const session = await this.sessionRepo.findOne({
      where: {
        id: jwtRefreshPayload.sessionId,
      },
    });

    if (!session) {
      throw new UnauthorizedException();
    }

    if (session.hash !== jwtRefreshPayload.hash) {
      throw new UnauthorizedException();
    }

    const hash = crypto
      .createHash('sha256')
      .update(randomStringGenerator())
      .digest('hex');

    await this.sessionRepo.update(session.id, {
      hash,
    });

    const { accessToken, refreshToken, tokenExpires } =
      await this.getTokensData({
        userId: session.userId as string,
        sessionId: session.id,
        hash,
      });

    return {
      accessToken,
      refreshToken,
      tokenExpires,
    };
  }

  async logout(sessionId: string) {
    await this.sessionRepo.delete(sessionId);
  }

  async changePassword(
    userId: string,
    input: ChangePasswordInput,
  ): Promise<void> {
    const userLogin = await this.userLoginService.getByUserId(userId);
    if (!userLogin) {
      throw new UnauthorizedException('Invalid credentials');
    }
    const { currentPassword, newPassword } = input;
    // Verify current password
    const isPasswordValid =
      userLogin.password &&
      (await bcrypt.compare(currentPassword, userLogin.password));

    if (!isPasswordValid) {
      throw new ConflictException('Current password is incorrect');
    }

    // Hash and update with new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    userLogin.password = hashedPassword;

    await this.userLoginService.update(userLogin.id, {
      password: hashedPassword,
      passwordChangedAt: new Date(),
    });
  }
}
