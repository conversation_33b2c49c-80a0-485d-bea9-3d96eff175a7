import { HttpErrorException } from '@app/common/exception/http-error.exception';
import { AUTH_ERROR_CODES } from '@app/modules/auth/auth.error-code';
import { USER_ERROR_CODES } from '@app/modules/user/user.error-code';
import { 
  EnableTwoFactorDto, 
  TwoFactorSetupResponseDto, 
  TwoFactorStatusDto,
  VerifyTwoFactorDto,
  DisableTwoFactorDto
} from '@app/modules/auth/dto/two-factor.dto';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { UserService } from '@app/modules/user/user.service';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';

@Injectable()
export class TwoFactorService {
  constructor(
    private readonly userService: UserService,
    private readonly configService: ConfigService,
  ) {}

  async generateTwoFactorSecret(userId: string): Promise<TwoFactorSetupResponseDto> {
    const user = await this.userService.findById(userId);
    if (!user) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
    }

    if (user.isTwoFactorEnabled) {
      throw new HttpErrorException({
        code: 'TWO_FACTOR_ALREADY_ENABLED',
        statusCode: 400,
        message: 'Two-factor authentication is already enabled',
      });
    }

    // Generate a secret
    const secret = speakeasy.generateSecret({
      name: user.email,
      issuer: this.configService.get('app.name', 'MyApp'),
      length: 32,
    });

    // Store the temporary secret (not yet confirmed)
    await this.userService.update(userId, {
      twoFactorSecret: secret.base32,
    });

    // Generate QR code
    const qrCodeDataUrl = await QRCode.toDataURL(secret.otpauth_url!);

    return {
      secret: secret.base32,
      otpAuthUrl: secret.otpauth_url!,
      qrCodeDataUrl,
    };
  }

  async enableTwoFactor(userId: string, dto: EnableTwoFactorDto): Promise<void> {
    const user = await this.userService.findById(userId);
    if (!user) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
    }

    if (user.isTwoFactorEnabled) {
      throw new HttpErrorException({
        code: 'TWO_FACTOR_ALREADY_ENABLED',
        statusCode: 400,
        message: 'Two-factor authentication is already enabled',
      });
    }

    if (!user.twoFactorSecret) {
      throw new HttpErrorException({
        code: 'TWO_FACTOR_SECRET_NOT_FOUND',
        statusCode: 400,
        message: 'Two-factor secret not found. Please generate a new secret first.',
      });
    }

    // Verify the TOTP code
    const isValid = this.verifyTotpCode(user.twoFactorSecret!, dto.totpCode);
    if (!isValid) {
      throw new HttpErrorException({
        code: 'INVALID_TOTP_CODE',
        statusCode: 400,
        message: 'Invalid TOTP code',
      });
    }

    // Enable two-factor authentication
    await this.userService.update(userId, {
      isTwoFactorEnabled: true,
    });
  }

  async verifyTwoFactor(userId: string, dto: VerifyTwoFactorDto): Promise<boolean> {
    const user = await this.userService.findById(userId);
    if (!user) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
    }

    if (!user.isTwoFactorEnabled || !user.twoFactorSecret) {
      throw new HttpErrorException({
        code: 'TWO_FACTOR_NOT_ENABLED',
        statusCode: 400,
        message: 'Two-factor authentication is not enabled',
      });
    }

    return this.verifyTotpCode(user.twoFactorSecret!, dto.totpCode);
  }

  async disableTwoFactor(userId: string, dto: DisableTwoFactorDto): Promise<void> {
    const user = await this.userService.findById(userId);
    if (!user) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
    }

    if (!user.isTwoFactorEnabled) {
      throw new HttpErrorException({
        code: 'TWO_FACTOR_NOT_ENABLED',
        statusCode: 400,
        message: 'Two-factor authentication is not enabled',
      });
    }

    // Verify the TOTP code before disabling
    const isValid = this.verifyTotpCode(user.twoFactorSecret!, dto.totpCode);
    if (!isValid) {
      throw new HttpErrorException({
        code: 'INVALID_TOTP_CODE',
        statusCode: 400,
        message: 'Invalid TOTP code',
      });
    }

    // Disable two-factor authentication and remove secret
    await this.userService.update(userId, {
      isTwoFactorEnabled: false,
      twoFactorSecret: null,
    });
  }

  async getTwoFactorStatus(userId: string): Promise<TwoFactorStatusDto> {
    const user = await this.userService.findById(userId);
    if (!user) {
      throw new HttpErrorException(USER_ERROR_CODES.USER_NOT_FOUND);
    }

    return {
      isTwoFactorEnabled: user.isTwoFactorEnabled,
    };
  }

  private verifyTotpCode(secret: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 2, // Allow 2 time steps before and after current time
    });
  }

  async requiresTwoFactorAuth(userId: string): Promise<boolean> {
    const user = await this.userService.findById(userId);
    return user?.isTwoFactorEnabled || false;
  }
}
