import validateConfig from '@app/common/config/validate-config';
import { registerAs } from '@nestjs/config';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import ms from 'ms';

export type AuthConfigType = {
  secret?: string;
  publicKey?: string;
  privateKey?: string;
  expires?: ms.StringValue;
  algorithm?: string;
  refreshSecret?: string;
  publicRefreshKey?: string;
  privateRefreshKey?: string;
  refreshExpires?: ms.StringValue;
  refreshAlgorithm?: string;
  forgotSecret?: string;
  forgotExpires?: ms.StringValue;
  confirmEmailSecret?: string;
  confirmEmailExpires?: ms.StringValue;
  sessionExpires: ms.StringValue;
  loginPasswordLessEmail?: {
    expires: ms.StringValue;
    length: number;
    templateEmail?: string;
    subjectEmail: string;
  };
  resetPassword?: {
    expires: ms.StringValue;
    otpCodeLength: number;
    templateEmailOtp?: string;
    subjectEmailOtp: string;
    tokenLength: number;
    templateEmailToken?: string;
    subjectEmailToken: string;
    resetPasswordUrl?: string;
  };
  facebook?: {
    clientId: string;
    clientSecret: string;
    redirectUrl: string;
  };
  google?: {
    clientId: string;
    clientSecret: string;
    callbackURL: string;
  };
  microsoft?: {
    clientId: string;
    clientSecret: string;
    callbackURL: string;
    tenant: string;
  };
};

class EnvironmentVariablesValidator {
  @IsString()
  AUTH_JWT_SECRET: string;

  @IsString()
  AUTH_JWT_TOKEN_EXPIRES_IN: string;

  @IsString()
  AUTH_REFRESH_SECRET: string;

  @IsString()
  AUTH_REFRESH_TOKEN_EXPIRES_IN: string;

  @IsString()
  AUTH_FORGOT_SECRET: string;

  @IsString()
  AUTH_FORGOT_TOKEN_EXPIRES_IN: string;

  @IsString()
  AUTH_CONFIRM_EMAIL_SECRET: string;

  @IsString()
  AUTH_CONFIRM_EMAIL_TOKEN_EXPIRES_IN: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_FACEBOOK_CLIENT_ID: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_FACEBOOK_CLIENT_SECRET: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_FACEBOOK_REDIRECT_URL: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_GOOGLE_CLIENT_ID: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_GOOGLE_CLIENT_SECRET: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_GOOGLE_REDIRECT_URL: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_AZURE_CLIENT_ID: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_AZURE_CLIENT_SECRET: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_AZURE_CALLBACK_URL: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_AZURE_RESOURCE: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_AZURE_TENANT: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_SESSION_EXPIRES_IN: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_LOGIN_PASSWORD_LESS_EMAIL_EXPIRES_IN: string;

  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  AUTH_LOGIN_PASSWORD_LESS_EMAIL_LENGTH: number;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_LOGIN_PASSWORD_LESS_EMAIL_TEMPLATE: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_LOGIN_PASSWORD_LESS_EMAIL_SUBJECT_EMAIL: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_EXPIRES_IN: string;

  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  AUTH_RESET_PASSWORD_OTP_CODE_LENGTH: number;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_OTP_CODE_TEMPLATE: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_OTP_CODE_SUBJECT_EMAIL: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_TOKEN_LENGTH: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_TOKEN_TEMPLATE: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_TOKEN_SUBJECT_EMAIL: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  AUTH_RESET_PASSWORD_URL: string;
}

export default registerAs<AuthConfigType>('auth', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    secret: process.env.AUTH_JWT_SECRET,
    expires: process.env.AUTH_JWT_TOKEN_EXPIRES_IN as ms.StringValue,
    refreshSecret: process.env.AUTH_REFRESH_SECRET,
    refreshExpires: process.env.AUTH_REFRESH_TOKEN_EXPIRES_IN as ms.StringValue,
    forgotSecret: process.env.AUTH_FORGOT_SECRET,
    forgotExpires: process.env.AUTH_FORGOT_TOKEN_EXPIRES_IN as ms.StringValue,
    confirmEmailSecret: process.env.AUTH_CONFIRM_EMAIL_SECRET,
    confirmEmailExpires: process.env
      .AUTH_CONFIRM_EMAIL_TOKEN_EXPIRES_IN as ms.StringValue,
    publicKey: process.env.AUTH_JWT_PUBLIC_KEY,
    privateKey: process.env.AUTH_JWT_PRIVATE_KEY,
    publicRefreshKey: process.env.AUTH_REFRESH_PUBLIC_KEY,
    privateRefreshKey: process.env.AUTH_REFRESH_PRIVATE_KEY,
    loginPasswordLessEmail: {
      expires:
        (process.env
          .AUTH_LOGIN_PASSWORD_LESS_EMAIL_EXPIRES_IN as ms.StringValue) || '5m',
      length: Number(process.env.AUTH_LOGIN_PASSWORD_LESS_EMAIL_LENGTH) || 6,
      template: process.env.AUTH_LOGIN_PASSWORD_LESS_EMAIL_TEMPLATE,
      subjectEmail:
        process.env.AUTH_LOGIN_PASSWORD_LESS_EMAIL_SUBJECT_EMAIL || 'Login OTP',
    },
    resetPassword: {
      expires:
        (process.env.AUTH_RESET_PASSWORD_EXPIRES_IN as ms.StringValue) || '5m',
      otpCodeLength:
        Number(process.env.AUTH_RESET_PASSWORD_OTP_CODE_LENGTH) || 6,
      templateEmailOtp: process.env.AUTH_RESET_PASSWORD_OTP_CODE_TEMPLATE,
      subjectEmailOtp:
        process.env.AUTH_RESET_PASSWORD_OTP_CODE_SUBJECT_EMAIL ||
        'Reset Password',
      tokenLength: Number(process.env.AUTH_RESET_PASSWORD_TOKEN_LENGTH) || 6,
      templateEmailToken: process.env.AUTH_RESET_PASSWORD_TOKEN_TEMPLATE,
      subjectEmailToken:
        process.env.AUTH_RESET_PASSWORD_TOKEN_SUBJECT_EMAIL || 'Reset Password',
      resetPasswordUrl: process.env.AUTH_RESET_PASSWORD_URL,
    },
    sessionExpires:
      (process.env.AUTH_SESSION_EXPIRES_IN as ms.StringValue) || '5m',
    facebook: {
      clientId: process.env.AUTH_FACEBOOK_CLIENT_ID || '',
      clientSecret: process.env.AUTH_FACEBOOK_CLIENT_SECRET || '',
      redirectUrl: process.env.AUTH_FACEBOOK_REDIRECT_URL || '',
    },
    google: {
      clientId: process.env.AUTH_GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.AUTH_GOOGLE_CLIENT_SECRET || '',
      callbackURL: process.env.AUTH_GOOGLE_REDIRECT_URL || '',
    },
    microsoft: {
      clientId: process.env.AUTH_MICROSOFT_CLIENT_ID || '',
      clientSecret: process.env.AUTH_MICROSOFT_CLIENT_SECRET || '',
      callbackURL: process.env.AUTH_MICROSOFT_CALLBACK_URL || '',
      tenant: process.env.AUTH_MICROSOFT_TENANT || '',
    },
  };
});
