# Two-Factor Authentication (2FA) Module

This module provides comprehensive two-factor authentication functionality using Time-based One-Time Passwords (TOTP) for your NestJS application.

## Features

- **TOTP-based 2FA**: Uses industry-standard Time-based One-Time Passwords
- **QR Code Generation**: Automatic QR code generation for easy authenticator app setup
- **Seamless Integration**: Integrates with existing authentication flow
- **Secure Storage**: Encrypted secret storage in the database
- **Flexible Guards**: Optional 2FA enforcement with decorators

## Installation

The required dependencies are already installed:
- `speakeasy` - For TOTP generation and verification
- `qrcode` - For QR code generation

## Database Migration

Run the migration to add 2FA fields to the users table:

```bash
npm run migration:run
```

This adds:
- `is_two_factor_enabled` (boolean, default: false)
- `two_factor_secret` (varchar, nullable)

## API Endpoints

### 1. Get 2FA Status
```
GET /api/v1/auth/2fa/status
Authorization: Bearer <token>
```

### 2. Generate 2FA Setup
```
POST /api/v1/auth/2fa/setup
Authorization: Bearer <token>
```

Returns:
- Base<PERSON> secret for manual entry
- OTP Auth URL for QR code
- Base64 QR code image

### 3. Enable 2FA
```
POST /api/v1/auth/2fa/enable
Authorization: Bearer <token>
Content-Type: application/json

{
  "totpCode": "123456"
}
```

### 4. Verify 2FA Code
```
POST /api/v1/auth/2fa/verify
Authorization: Bearer <token>
Content-Type: application/json

{
  "totpCode": "123456"
}
```

### 5. Disable 2FA
```
DELETE /api/v1/auth/2fa/disable
Authorization: Bearer <token>
Content-Type: application/json

{
  "totpCode": "123456"
}
```

### 6. Complete 2FA Login
```
POST /api/v1/auth/login/2fa/complete
Content-Type: application/json

{
  "tempToken": "temporary_token_from_login",
  "totpCode": "123456"
}
```

## Login Flow with 2FA

### Standard Login (2FA Disabled)
1. `POST /api/v1/auth/login/email` → Returns access token directly

### 2FA-Enabled Login
1. `POST /api/v1/auth/login/email` → Returns temporary token and `requiresTwoFactor: true`
2. `POST /api/v1/auth/login/2fa/complete` → Returns access token after TOTP verification

## Usage Examples

### Frontend Integration

```typescript
// 1. Setup 2FA
const setupResponse = await fetch('/api/v1/auth/2fa/setup', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${accessToken}` }
});
const { qrCodeDataUrl, secret } = await setupResponse.json();

// Display QR code to user
document.getElementById('qr-code').src = qrCodeDataUrl;

// 2. Enable 2FA after user scans QR code
const enableResponse = await fetch('/api/v1/auth/2fa/enable', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ totpCode: userEnteredCode })
});

// 3. Handle login with 2FA
const loginResponse = await fetch('/api/v1/auth/login/email', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password })
});

const loginData = await loginResponse.json();

if (loginData.requiresTwoFactor) {
  // Prompt user for TOTP code
  const totpCode = prompt('Enter your 2FA code:');
  
  const completeResponse = await fetch('/api/v1/auth/login/2fa/complete', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ 
      tempToken: loginData.tempToken, 
      totpCode 
    })
  });
  
  const { accessToken } = await completeResponse.json();
  // Store access token
} else {
  // Standard login, use accessToken directly
  const { accessToken } = loginData;
}
```

### Backend Service Usage

```typescript
import { TwoFactorService } from '@app/modules/auth/services/two-factor.service';

@Injectable()
export class MyService {
  constructor(private readonly twoFactorService: TwoFactorService) {}

  async checkUserTwoFactor(userId: string) {
    const status = await this.twoFactorService.getTwoFactorStatus(userId);
    return status.isTwoFactorEnabled;
  }

  async verifyUserCode(userId: string, code: string) {
    return this.twoFactorService.verifyTwoFactor(userId, { totpCode: code });
  }
}
```

## Security Considerations

1. **Temporary Tokens**: Login temporary tokens expire in 10 minutes
2. **TOTP Window**: Accepts codes within 2 time steps (±60 seconds)
3. **Secret Storage**: Secrets are stored securely in the database
4. **Rate Limiting**: Consider implementing rate limiting on 2FA endpoints
5. **Backup Codes**: Consider implementing backup codes for account recovery

## Recommended Authenticator Apps

- Google Authenticator
- Microsoft Authenticator
- Authy
- 1Password
- Bitwarden

## Error Codes

- `TWO_FACTOR_ALREADY_ENABLED`: 2FA is already enabled for the user
- `TWO_FACTOR_NOT_ENABLED`: 2FA is not enabled for the user
- `TWO_FACTOR_SECRET_NOT_FOUND`: Secret not found, need to generate new setup
- `INVALID_TOTP_CODE`: Invalid or expired TOTP code
- `INVALID_TEMP_TOKEN`: Invalid or expired temporary token
- `INVALID_TOKEN_TYPE`: Wrong token type provided

## Testing

Use the following test TOTP secret for development:
```
JBSWY3DPEHPK3PXP
```

This generates predictable codes for testing purposes.
