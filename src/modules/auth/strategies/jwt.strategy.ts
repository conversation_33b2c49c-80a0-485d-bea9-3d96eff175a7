import { AuthConfigType } from '@app/modules/auth/auth.config';
import { JwtPayloadType } from '@modules/auth/strategies/types/jwt-payload.type';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    configService: ConfigService<{
      auth: AuthConfigType;
    }>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.getOrThrow('auth.secret', { infer: true }),
    });
  }

  public validate(payload: JwtPayloadType): JwtPayloadType {
    if (!payload.sub) {
      throw new UnauthorizedException();
    }
    return payload;
  }
}
