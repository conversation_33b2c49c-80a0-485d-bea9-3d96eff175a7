import { AuthConfigType } from '@app/modules/auth/auth.config';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { Profile, Strategy } from 'passport-facebook';

@Injectable()
export class FacebookStrategy extends PassportStrategy(Strategy, 'facebook') {
  constructor(
    private readonly configService: ConfigService<{
      auth: AuthConfigType;
    }>,
  ) {
    const facebookConfig = configService.getOrThrow('auth.facebook', {
      infer: true,
    });
    const clientID = facebookConfig.clientId;
    const clientSecret = facebookConfig.clientSecret;
    const redirectUrl = facebookConfig.redirectUrl;
    super({
      clientID,
      clientSecret,
      callbackURL: redirectUrl,
      scope: 'email',
      profileFields: ['emails', 'name'],
      passReqToCallback: true,
    });
  }

  authenticate(req: Request, options: any): void {
    if (req.query.callback) {
      return super.authenticate(req, {
        ...options,
        state: req.query.callback,
      });
    }
    return super.authenticate(req, options);
  }

  validate(
    request: Request,
    accessToken: string,
    refreshToken: string,
    profile: Profile,
    done: (err: any, user: any, info?: any) => void,
  ): any {
    const { name, emails } = profile;
    const email = emails ? emails[0]?.value : null;
    const firstName = name?.givenName;
    const lastName = name?.familyName;
    const picture = profile.photos ? profile.photos[0].value : null;
    console.log(profile);
    const payload = {
      id: profile.id,
      email: email,
      firstName: firstName,
      lastName: lastName,
      picture: picture,
      accessToken,
      refreshToken,
      state: request.query.state,
    };

    done(null, payload);
  }
}
