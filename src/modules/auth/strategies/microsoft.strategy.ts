import { AuthConfigType } from '@app/modules/auth/auth.config';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { Strategy } from 'passport-microsoft';

@Injectable()
export class MicrosoftStrategy extends PassportStrategy(Strategy, 'microsoft') {
  constructor(
    configService: ConfigService<{
      auth: AuthConfigType;
    }>,
  ) {
    const microsoftConfig = configService.getOrThrow('auth.microsoft', {
      infer: true,
    });
    super({
      clientID: microsoftConfig.clientId,
      clientSecret: microsoftConfig.clientSecret,
      callbackURL: microsoftConfig.callbackURL,
      tenant: microsoftConfig.tenant,
      passReqToCallback: true,
      scope: ['user.read'],
    });
  }

  authenticate(req: Request, options: any): void {
    if (req.query.callback) {
      return super.authenticate(req, {
        ...options,
        state: req.query.callback,
      });
    }
    return super.authenticate(req, options);
  }

  validate(
    request: Request,
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: (err: any, user: any, info?: any) => void,
  ) {
    try {
      console.log(profile);
      const { name, emails } = profile;
      const { givenName, familyName } = name;
      const user = {
        id: profile.id,
        email: emails[0].value,
        firstName: givenName,
        lastName: familyName,
        picture: null,
        accessToken,
        refreshToken,
        state: request.query.state,
      };
      done(null, user);
    } catch (err) {
      done(err, null);
    }
  }
}
