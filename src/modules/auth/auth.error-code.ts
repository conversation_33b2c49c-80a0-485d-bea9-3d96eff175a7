import { ErrorCode } from '@app/common/types';
import { HttpStatus } from '@nestjs/common';

const AUTH_NAMESPACE = 'AUTH';

type AuthErrorCodes =
  | 'INVALID_CREDENTIALS'
  | 'INVALID_OTP'
  | 'INVALID_RESET_PASSWORD_TOKEN'
  | 'SESSION_EXPIRED'
  | 'SESSION_NOT_FOUND';

export const AUTH_ERROR_CODES: Record<AuthErrorCodes, ErrorCode> = {
  INVALID_CREDENTIALS: {
    code: `${AUTH_NAMESPACE}:10000`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Invalid credentials',
  },
  INVALID_OTP: {
    code: `${AUTH_NAMESPACE}:10001`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Invalid OTP',
  },
  INVALID_RESET_PASSWORD_TOKEN: {
    code: `${AUTH_NAMESPACE}:10002`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Invalid reset password token',
  },
  SESSION_EXPIRED: {
    code: `${AUTH_NAMESPACE}:10003`,
    statusCode: HttpStatus.UNAUTHORIZED,
    message: 'Session expired',
  },
  SESSION_NOT_FOUND: {
    code: `${AUTH_NAMESPACE}:10004`,
    statusCode: HttpStatus.NOT_FOUND,
    message: 'Session not found',
  },
};
