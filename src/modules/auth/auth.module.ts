import authConfig, { AuthConfigType } from '@app/modules/auth/auth.config';
import { SocialController } from '@app/modules/auth/controllers/social.controller';
import { OtpEntity } from '@app/modules/auth/entities/otp.entity';
import { SessionEntity } from '@app/modules/auth/entities/session.entity';
import { SocialEntity } from '@app/modules/auth/entities/social.entity';
import { UserLoginEntity } from '@app/modules/auth/entities/user-login.entity';
import { SocialService } from '@app/modules/auth/services/social.service';
import { UserLoginService } from '@app/modules/auth/services/user-login.service';
import { FacebookStrategy } from '@app/modules/auth/strategies/facebook.strategy';
import { GoogleStrategy } from '@app/modules/auth/strategies/google.strategy';
import { JwtStrategy } from '@app/modules/auth/strategies/jwt.strategy';
import { MicrosoftStrategy } from '@app/modules/auth/strategies/microsoft.strategy';
import { MailerModule } from '@app/modules/mailer/mailer.module';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { UserModule } from '@app/modules/user/user.module';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthController } from './controllers/auth.controller';
import { AuthService } from './services/auth.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [authConfig],
    }),
    TypeOrmModule.forFeature([
      SessionEntity,
      UserEntity,
      SocialEntity,
      OtpEntity,
      UserLoginEntity,
    ]),
    PassportModule,
    UserModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const algorithm = configService.get<AuthConfigType>('auth.algorithm', {
          infer: true,
        });
        return {
          secret: configService.get<AuthConfigType>('auth.jwt.secret', {
            infer: true,
          }),
          privateKey: configService.get<AuthConfigType>('auth.jwt.privateKey', {
            infer: true,
          }),
          publicKey: configService.get<AuthConfigType>('auth.publicKey', {
            infer: true,
          }),
          signOptions: algorithm
            ? {
                algorithm,
              }
            : undefined,
        };
      },
      inject: [ConfigService],
    }),
    MailerModule,
  ],
  controllers: [AuthController, SocialController],
  providers: [
    AuthService,
    UserLoginService,
    JwtStrategy,
    SocialService,
    FacebookStrategy,
    GoogleStrategy,
    MicrosoftStrategy,
  ],
  exports: [
    AuthService,
    UserLoginService,
    JwtModule,
    PassportModule,
    SocialService,
  ],
})
export class AuthModule {}
