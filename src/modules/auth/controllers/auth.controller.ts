import { getIp<PERSON>ddress, getUserAgent } from '@app/common/utils/request';
import { Public } from '@app/modules/auth/decorators/public.decorator';
import { ChangePasswordInput } from '@app/modules/auth/dto/change-password.dto';
import {
  LoginPasswordLessEmailInput,
  VerifyOtpLoginPasswordLessEmailInput,
} from '@app/modules/auth/dto/login-password-less-email.dto';
import { LoginEmailDto } from '@app/modules/auth/dto/login.dto';
import { RefreshTokenDto } from '@app/modules/auth/dto/refresh-token.dto';
import { RegisterResponseDto } from '@app/modules/auth/dto/register-response.dto';
import { RegisterDto } from '@app/modules/auth/dto/register.dto';
import {
  RequestResetPasswordByEmailLinkInput,
  ResetPasswordByEmailLinkInput,
} from '@app/modules/auth/dto/reset-password-by-email-link.dto';
import {
  RequestResetPasswordByEmailOtpInput,
  ResetPasswordByEmailOtpInput,
} from '@app/modules/auth/dto/reset-password-email-otp.dto';
import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import { AuthService } from '@app/modules/auth/services/auth.service';
import { UserProfileDto } from '@app/modules/user/dto/user-profile.dto';
import { User } from '@modules/auth/decorators/user.decorator';
import { JwtPayloadType } from '@modules/auth/strategies/types/jwt-payload.type';
import {
  Body,
  ConflictException,
  Controller,
  HttpCode,
  HttpStatus,
  Patch,
  Post,
  Req,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';

@ApiTags('Authentication')
@Controller({ path: 'auth', version: '1' })
export class AuthController {
  constructor(private readonly authService: AuthService) {
    console.log('AuthController constructor');
  }

  @ApiOperation({ summary: 'Register new user' })
  @ApiBody({ type: RegisterDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'User registered successfully',
    type: RegisterResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Email already exists',
  })
  @ApiBody({ type: RegisterDto })
  @Public()
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  async register(@Body() registerDto: RegisterDto, @Req() req: Request) {
    try {
      const userAgent = req.headers['user-agent'] || '';
      const ipAddress = req.ip || '';

      return await this.authService.register(registerDto, userAgent, ipAddress);
    } catch (error) {
      if (error.message === 'Email already exists') {
        throw new ConflictException(error.message);
      }
      throw error;
    }
  }

  @ApiOperation({ summary: 'Login by email' })
  @ApiBody({ type: LoginEmailDto })
  @Public()
  @Post('login/email')
  @HttpCode(HttpStatus.OK)
  async loginByEmail(@Body() loginDto: LoginEmailDto, @Req() req: Request) {
    const userLogin = await this.authService.validateUser(
      loginDto.email,
      loginDto.password,
    );

    if (!userLogin) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const userAgent = getUserAgent(req);
    const ipAddress = getIpAddress(req);

    return this.authService.login(userLogin, userAgent, ipAddress);
  }

  @ApiOperation({ summary: 'Login with password less email' })
  @ApiBody({ type: LoginPasswordLessEmailInput })
  @Public()
  @Post('login/password-less/email')
  @HttpCode(HttpStatus.OK)
  async loginPasswordLessEmail(@Body() input: LoginPasswordLessEmailInput) {
    return this.authService.loginPasswordLessEmail(input);
  }

  @ApiOperation({ summary: 'Verify OTP for login with password less email' })
  @ApiBody({ type: VerifyOtpLoginPasswordLessEmailInput })
  @Public()
  @Post('login/password-less/email/verify')
  @HttpCode(HttpStatus.OK)
  async verifyOtpLoginPasswordLessEmail(
    @Body() input: VerifyOtpLoginPasswordLessEmailInput,
    @Req() req: Request,
  ) {
    const userAgent = getUserAgent(req);
    const ipAddress = getIpAddress(req);

    return this.authService.verifyOtpLoginPasswordLessEmail(
      input,
      userAgent,
      ipAddress,
    );
  }

  @ApiOperation({ summary: 'Refresh token' })
  @ApiBody({ type: RefreshTokenDto })
  @Post('refresh')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  refreshToken(@Body() input: RefreshTokenDto) {
    return this.authService.refreshToken(input);
  }

  @ApiOperation({ summary: 'Logout' })
  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  async logout(@User() jwt: JwtPayloadType) {
    await this.authService.logout(jwt.sessionId);
    return { message: 'Successfully logged out' };
  }

  @Patch('/change-password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Change user password' })
  @ApiResponse({
    status: 200,
    description: 'Password changed successfully',
    type: UserProfileDto,
  })
  async changePassword(
    @User() user: JwtPayloadType,
    @Body() input: ChangePasswordInput,
  ) {
    await this.authService.changePassword(user.sub, input);
    return { message: 'Password changed successfully' };
  }

  @ApiOperation({ summary: 'Request reset password by email OTP' })
  @ApiBody({ type: RequestResetPasswordByEmailOtpInput })
  @Post('reset-password/email/otp/request')
  @Public()
  async requestResetPasswordByEmailOtp(
    @Body() input: RequestResetPasswordByEmailOtpInput,
  ) {
    return this.authService.requestResetPasswordByEmailOtp(input);
  }

  @ApiOperation({ summary: 'Reset password by email OTP' })
  @Post('reset-password/email/otp')
  @ApiBody({ type: ResetPasswordByEmailOtpInput })
  @Public()
  async verifyResetPasswordByEmailOtp(
    @Body() input: ResetPasswordByEmailOtpInput,
  ) {
    return this.authService.resetPasswordByEmailOtp(input);
  }

  @ApiOperation({ summary: 'Request reset password by email link' })
  @ApiBody({ type: RequestResetPasswordByEmailLinkInput })
  @Post('reset-password/email/link/request')
  @Public()
  async requestResetPasswordByEmailLink(
    @Body() input: RequestResetPasswordByEmailLinkInput,
  ) {
    return this.authService.requestResetPasswordByEmailLink(input);
  }

  @ApiOperation({ summary: 'Reset password by email link' })
  @Post('reset-password/email/link')
  @ApiBody({ type: ResetPasswordByEmailLinkInput })
  @Public()
  async resetPasswordByEmailLink(@Body() input: ResetPasswordByEmailLinkInput) {
    return this.authService.resetPasswordByEmailLink(input);
  }
}
