import { getIpAddress, getUserAgent } from '@app/common/utils/request';
import { AuthConfigType } from '@app/modules/auth/auth.config';
import { SocialProviderEnum } from '@app/modules/auth/enums/social-provider.enum';
import { FacebookAuthGuard } from '@app/modules/auth/guards/facebook-auth.guard';
import { GoogleAuthGuard } from '@app/modules/auth/guards/google-auth.guard';
import { MicrosoftAuthGuard } from '@app/modules/auth/guards/microsoft.guard';
import { SocialService } from '@app/modules/auth/services/social.service';
import {
  Controller,
  Get,
  HttpStatus,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';

@Controller({
  path: 'auth/social',
  version: '1',
})
export class SocialController {
  constructor(
    private readonly socialService: SocialService,
    private readonly configService: ConfigService<{
      auth: AuthConfigType;
    }>,
  ) {}

  @Get('/facebook/login')
  @UseGuards(FacebookAuthGuard)
  facebookLogin() {
    return HttpStatus.OK;
  }

  @Get('/facebook/redirect')
  @UseGuards(FacebookAuthGuard)
  async facebookLoginRedirect(@Req() req: Request, @Res() res: Response) {
    const callback = req.query.state as string;
    const user = req.user as any;
    console.log(callback, user);

    const { authorizationCode } = await this.socialService.saveSocialUser({
      provider: SocialProviderEnum.FACEBOOK,
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      picture: user.picture,
      accessToken: user.accessToken,
      refreshToken: user.refreshToken,
    });
    const urlCallback = new URL(callback);
    urlCallback.searchParams.append('code', authorizationCode);
    console.log(urlCallback.toString());
    return res.redirect(urlCallback.toString());
  }

  @Get('/facebook/delete')
  facebookDelete(@Req() req: Request, @Query() query: any) {
    const providerId = query.providerId as string;
    console.log(query);
    return {
      statusCode: HttpStatus.OK,
      data: providerId,
    };
  }

  @Get('/google/login')
  @UseGuards(GoogleAuthGuard)
  googleAuth() {
    return HttpStatus.OK;
  }

  @Get('/google/redirect')
  @UseGuards(GoogleAuthGuard)
  async googleLoginRedirect(
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    const callback = req.query.state as string;
    const user = req.user as any;
    console.log(callback, user);

    const { authorizationCode } = await this.socialService.saveSocialUser({
      provider: SocialProviderEnum.GOOGLE,
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      picture: user.picture,
      accessToken: user.accessToken,
      refreshToken: user.refreshToken,
    });
    const urlCallback = new URL(callback);
    urlCallback.searchParams.append('code', authorizationCode);
    console.log(urlCallback.toString());
    return res.redirect(urlCallback.toString());
  }

  @Get('/login/code')
  googleCallback(@Query('code') code: string, @Req() req: Request) {
    const ipAddress = getIpAddress(req);
    const userAgent = getUserAgent(req);
    return this.socialService.loginWithCode(code, ipAddress, userAgent);
  }

  @Get('/microsoft/login')
  @UseGuards(MicrosoftAuthGuard)
  microsoftAuth() {
    return HttpStatus.OK;
  }

  @Get('/microsoft/redirect')
  @UseGuards(MicrosoftAuthGuard)
  async microsoftLoginRedirect(
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    const callback = req.query.state as string;
    const user = req.user as any;
    console.log(callback, user);

    const { authorizationCode } = await this.socialService.saveSocialUser({
      provider: SocialProviderEnum.MICROSOFT,
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      picture: user.picture,
      accessToken: user.accessToken,
      refreshToken: user.refreshToken,
    });
    const urlCallback = new URL(callback);
    urlCallback.searchParams.append('code', authorizationCode);
    console.log(urlCallback.toString());
    return res.redirect(urlCallback.toString());
  }
}
