import { JwtPayloadType } from '@app/modules/auth/strategies/types/jwt-payload.type';
import { 
  EnableTwoFactorDto, 
  TwoFactorSetupResponseDto, 
  TwoFactorStatusDto,
  VerifyTwoFactorDto,
  DisableTwoFactorDto
} from '@app/modules/auth/dto/two-factor.dto';
import { JwtAuthGuard } from '@app/modules/auth/guards/jwt-auth.guard';
import { TwoFactorService } from '@app/modules/auth/services/two-factor.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { User } from '@app/modules/auth/decorators/user.decorator';

@ApiTags('Two-Factor Authentication')
@ApiBearerAuth()
@Controller({ path: 'auth/2fa', version: '1' })
@UseGuards(JwtAuthGuard)
export class TwoFactorController {
  constructor(private readonly twoFactorService: TwoFactorService) {}

  @ApiOperation({ summary: 'Get two-factor authentication status' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Two-factor authentication status retrieved successfully',
    type: TwoFactorStatusDto,
  })
  @Get('status')
  @HttpCode(HttpStatus.OK)
  async getStatus(@User() user: JwtPayloadType): Promise<TwoFactorStatusDto> {
    return this.twoFactorService.getTwoFactorStatus(user.sub);
  }

  @ApiOperation({ summary: 'Generate two-factor authentication setup' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Two-factor authentication setup generated successfully',
    type: TwoFactorSetupResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Two-factor authentication is already enabled',
  })
  @Post('setup')
  @HttpCode(HttpStatus.OK)
  async generateSetup(@User() user: JwtPayloadType): Promise<TwoFactorSetupResponseDto> {
    return this.twoFactorService.generateTwoFactorSecret(user.sub);
  }

  @ApiOperation({ summary: 'Enable two-factor authentication' })
  @ApiBody({ type: EnableTwoFactorDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Two-factor authentication enabled successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid TOTP code or two-factor authentication already enabled',
  })
  @Post('enable')
  @HttpCode(HttpStatus.OK)
  async enable(
    @User() user: JwtPayloadType,
    @Body() dto: EnableTwoFactorDto,
  ): Promise<{ message: string }> {
    await this.twoFactorService.enableTwoFactor(user.sub, dto);
    return { message: 'Two-factor authentication enabled successfully' };
  }

  @ApiOperation({ summary: 'Verify two-factor authentication code' })
  @ApiBody({ type: VerifyTwoFactorDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Two-factor authentication code verified successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid TOTP code or two-factor authentication not enabled',
  })
  @Post('verify')
  @HttpCode(HttpStatus.OK)
  async verify(
    @User() user: JwtPayloadType,
    @Body() dto: VerifyTwoFactorDto,
  ): Promise<{ valid: boolean }> {
    const valid = await this.twoFactorService.verifyTwoFactor(user.sub, dto);
    return { valid };
  }

  @ApiOperation({ summary: 'Disable two-factor authentication' })
  @ApiBody({ type: DisableTwoFactorDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Two-factor authentication disabled successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid TOTP code or two-factor authentication not enabled',
  })
  @Delete('disable')
  @HttpCode(HttpStatus.OK)
  async disable(
    @User() user: JwtPayloadType,
    @Body() dto: DisableTwoFactorDto,
  ): Promise<{ message: string }> {
    await this.twoFactorService.disableTwoFactor(user.sub, dto);
    return { message: 'Two-factor authentication disabled successfully' };
  }
}
