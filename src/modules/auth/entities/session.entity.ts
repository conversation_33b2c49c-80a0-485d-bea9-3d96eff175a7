import { CustomBaseEntity } from '@app/common/entities/base.entity';
import { Nullable } from '@app/common/types';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
@Entity('sessions')
export class SessionEntity extends CustomBaseEntity {
  @Column({
    name: 'user_id',
    type: 'uuid',
    nullable: true,
  })
  userId: Nullable<string>;

  @ManyToOne(() => UserEntity, {
    createForeignKeyConstraints: false,
    nullable: true,
  })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column({
    name: 'hash',
    type: 'varchar',
    length: 500,
  })
  hash: string;

  @Column({
    name: 'user_agent',
    type: 'varchar',
    length: 255,
  })
  userAgent: string;

  @Column({
    name: 'ip_address',
    type: 'varchar',
    length: 50,
  })
  ipAddress: string;

  isExpired(expiresIn: number): boolean {
    const now = new Date();
    const diff = now.getTime() - this.createdAt.getTime();
    return diff > expiresIn;
  }
}
