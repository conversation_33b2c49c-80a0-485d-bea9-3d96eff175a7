import { CustomBaseEntity } from '@app/common/entities/base.entity';
import { SocialProviderEnum } from '@app/modules/auth/enums/social-provider.enum';
import { Column, Entity, Unique } from 'typeorm';

@Entity('social')
@Unique(['provider', 'providerId'])
export class SocialEntity extends CustomBaseEntity {
  @Column({ type: 'varchar', length: 50 })
  provider: SocialProviderEnum;

  @Column({ type: 'varchar', length: 255 })
  providerId: string;

  @Column({ type: 'varchar', length: 128, nullable: true })
  email: string;

  @Column({ type: 'varchar', length: 128, nullable: true })
  firstName: string;

  @Column({ type: 'varchar', length: 128, nullable: true })
  lastName: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  picture: string;

  @Column({ type: 'text', nullable: true })
  accessToken: string;

  @Column({ type: 'text', nullable: true })
  refreshToken: string;
}
