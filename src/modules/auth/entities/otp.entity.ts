import { CustomBaseEntity } from '@app/common/entities/base.entity';
import { Nullable } from '@app/common/types';
import { UserLoginEntity } from '@app/modules/auth/entities/user-login.entity';
import { OtpTypeEnum } from '@app/modules/auth/enums/otp-type.enum';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

@Entity('otps')
export class OtpEntity extends CustomBaseEntity {
  // null is for case passwordless email login
  @Column({ name: 'user_id', type: 'uuid', nullable: true })
  userId: Nullable<string>;

  @Column({ name: 'otp', type: 'varchar', length: 10 })
  otp: string;

  @Column({ name: 'type', type: 'varchar', length: 50 })
  type: OtpTypeEnum;

  @ManyToOne(() => UserEntity, {
    createForeignKeyConstraints: false,
    nullable: true,
  })
  @JoinColumn({ name: 'user_id', referencedColumnName: 'id' })
  user: Nullable<UserEntity>;

  @ManyToOne(() => UserEntity, {
    createForeignKeyConstraints: false,
    nullable: true,
  })
  @JoinColumn({ name: 'user_id', referencedColumnName: 'id' })
  userLogin: Nullable<UserLoginEntity>;
}
