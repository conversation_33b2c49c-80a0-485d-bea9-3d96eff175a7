import { UserRoleEnum } from '@app/modules/user/enums/user-role.enum';
import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class TokenResponseDto {
  @Expose()
  @ApiProperty({ description: 'Access token' })
  accessToken: string;

  @Expose()
  @ApiProperty({ description: 'Refresh token' })
  refreshToken: string;

  @Expose()
  @ApiProperty({ description: 'Token expires in' })
  tokenExpires: number;
}

export class ProfileResponseDto {
  @Expose()
  @ApiProperty({ description: 'User ID' })
  id: string;

  @Expose()
  @ApiProperty({ description: 'User email' })
  email: string;

  @Expose()
  @ApiProperty({ description: 'User role', enum: UserRoleEnum })
  role: UserRoleEnum;
}

export class LoginResponseDto extends TokenResponseDto {
  @Expose()
  @ApiProperty({ description: 'User profile', type: ProfileResponseDto })
  @Type(() => ProfileResponseDto)
  user: ProfileResponseDto;

  @Expose()
  @ApiProperty({
    description: 'Whether two-factor authentication is required',
    example: false
  })
  requiresTwoFactor?: boolean;
}

export class TwoFactorLoginResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Temporary token for two-factor authentication',
    example: 'temp_token_123'
  })
  tempToken: string;

  @Expose()
  @ApiProperty({
    description: 'Whether two-factor authentication is required',
    example: true
  })
  requiresTwoFactor: boolean;

  @Expose()
  @ApiProperty({
    description: 'Message indicating next steps',
    example: 'Please provide your two-factor authentication code'
  })
  message: string;
}
