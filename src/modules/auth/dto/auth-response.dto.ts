import { UserRoleEnum } from '@app/modules/user/enums/user-role.enum';
import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class TokenResponseDto {
  @Expose()
  @ApiProperty({ description: 'Access token' })
  accessToken: string;

  @Expose()
  @ApiProperty({ description: 'Refresh token' })
  refreshToken: string;

  @Expose()
  @ApiProperty({ description: 'Token expires in' })
  tokenExpires: number;
}

export class ProfileResponseDto {
  @Expose()
  @ApiProperty({ description: 'User ID' })
  id: string;

  @Expose()
  @ApiProperty({ description: 'User email' })
  email: string;

  @Expose()
  @ApiProperty({ description: 'User role', enum: UserRoleEnum })
  role: UserRoleEnum;
}

export class LoginResponseDto extends TokenResponseDto {
  @Expose()
  @ApiProperty({ description: 'User profile', type: ProfileResponseDto })
  @Type(() => ProfileResponseDto)
  user: ProfileResponseDto;
}
