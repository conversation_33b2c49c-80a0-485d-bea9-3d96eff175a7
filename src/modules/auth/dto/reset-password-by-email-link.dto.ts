import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString } from 'class-validator';

export class RequestResetPasswordByEmailLinkInput {
  @ApiProperty({
    description: 'The email of the user',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;
}

export class ResetPasswordByEmailLinkInput {
  @ApiProperty({
    description: 'The token of the user',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    description: 'The new password of the user',
    example: 'Password123',
  })
  @IsString()
  @IsNotEmpty()
  newPassword: string;
}
