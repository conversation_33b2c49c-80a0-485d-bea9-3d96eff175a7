import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsNumberString } from 'class-validator';

export class LoginPasswordLessEmailInput {
  @ApiProperty({
    description: 'The email of the user',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;
}

export class VerifyOtpLoginPasswordLessEmailInput {
  @ApiProperty({
    description: 'The email of the user',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'The OTP code',
    example: '123456',
  })
  @IsNumberString()
  @IsNotEmpty()
  otp: string;
}
