import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsNumberString, IsString } from 'class-validator';

export class RequestResetPasswordByEmailOtpInput {
  @ApiProperty({
    description: 'The email of the user',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;
}

export class ResetPasswordByEmailOtpInput {
  @ApiProperty({
    description: 'The email of the user',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'The OTP code',
    example: '123456',
  })
  @IsNumberString()
  @IsNotEmpty()
  otp: string;

  @ApiProperty({
    description: 'The password',
    example: 'password123',
  })
  @IsString()
  @IsNotEmpty()
  newPassword: string;
}
