import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsString, Length } from 'class-validator';

export class EnableTwoFactorDto {
  @Expose()
  @ApiProperty({ 
    example: '123456',
    description: 'Six-digit TOTP code from authenticator app'
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: 'TOTP code must be exactly 6 digits' })
  totpCode: string;
}

export class VerifyTwoFactorDto {
  @Expose()
  @ApiProperty({ 
    example: '123456',
    description: 'Six-digit TOTP code from authenticator app'
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: 'TOTP code must be exactly 6 digits' })
  totpCode: string;
}

export class DisableTwoFactorDto {
  @Expose()
  @ApiProperty({ 
    example: '123456',
    description: 'Six-digit TOTP code from authenticator app'
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: 'TOTP code must be exactly 6 digits' })
  totpCode: string;
}

export class TwoFactorSetupResponseDto {
  @Expose()
  @ApiProperty({ 
    example: 'JBSWY3DPEHPK3PXP',
    description: 'Base32 encoded secret for manual entry'
  })
  secret: string;

  @Expose()
  @ApiProperty({ 
    example: 'otpauth://totp/MyApp:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=MyApp',
    description: 'OTP Auth URL for QR code generation'
  })
  otpAuthUrl: string;

  @Expose()
  @ApiProperty({ 
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
    description: 'Base64 encoded QR code image'
  })
  qrCodeDataUrl: string;
}

export class TwoFactorStatusDto {
  @Expose()
  @ApiProperty({
    example: true,
    description: 'Whether two-factor authentication is enabled'
  })
  isTwoFactorEnabled: boolean;
}

export class CompleteTwoFactorLoginDto {
  @Expose()
  @ApiProperty({
    example: 'temp_token_123',
    description: 'Temporary token received from initial login'
  })
  @IsString()
  @IsNotEmpty()
  tempToken: string;

  @Expose()
  @ApiProperty({
    example: '123456',
    description: 'Six-digit TOTP code from authenticator app'
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: 'TOTP code must be exactly 6 digits' })
  totpCode: string;
}
