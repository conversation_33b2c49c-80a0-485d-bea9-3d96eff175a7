import { ManualActionConfigType } from '@app/modules/manual-action/manual-action.config';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { BasicStrategy as Strategy } from 'passport-http';

@Injectable()
export class BasicAuthStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService<{
      manualAction: ManualActionConfigType;
    }>,
  ) {
    super();
  }

  validate(username: string, password: string): boolean {
    const configUsername = this.configService.getOrThrow(
      'manualAction.username',
      {
        infer: true,
      },
    );
    const configPassword = this.configService.getOrThrow<string>(
      'manualAction.password',
      {
        infer: true,
      },
    );

    if (!configUsername || !configPassword) {
      throw new UnauthorizedException('Basic auth credentials not configured');
    }

    if (username === configUsername && password === configPassword) {
      return true;
    }

    throw new UnauthorizedException('Invalid credentials');
  }
}
