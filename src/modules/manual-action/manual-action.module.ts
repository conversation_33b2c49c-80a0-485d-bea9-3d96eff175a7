import { manualActionConfig } from '@app/modules/manual-action/manual-action.config';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { BasicAuthStrategy } from './basic-auth.strategy';
import { ManualActionController } from './manual-action.controller';
import { ManualActionService } from './manual-action.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [manualActionConfig],
    }),
  ],
  controllers: [ManualActionController],
  providers: [ManualActionService, BasicAuthStrategy],
})
export class ManualActionModule {}
