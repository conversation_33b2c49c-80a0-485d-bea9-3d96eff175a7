import validateConfig from '@common/config/validate-config';
import { registerAs } from '@nestjs/config';
import { IsString } from 'class-validator';

export type ManualActionConfigType = {
  username: string;
  password: string;
};

class EnvironmentVariablesValidator {
  @IsString()
  OPENAI_API_KEY: string;
}

export const manualActionConfig = registerAs<ManualActionConfigType>(
  'manualAction',
  () => {
    validateConfig(process.env, EnvironmentVariablesValidator);
    return {
      username: process.env.MANUAL_ACTION_USERNAME ?? '',
      password: process.env.MANUAL_ACTION_PASSWORD ?? '',
    };
  },
);
