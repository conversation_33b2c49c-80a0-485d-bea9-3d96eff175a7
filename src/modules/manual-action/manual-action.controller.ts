import { Controller, Logger, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBasicAuth, ApiTags } from '@nestjs/swagger';
import { ManualActionService } from './manual-action.service';

@ApiTags('Manual Actions')
@ApiBasicAuth()
@UseGuards(AuthGuard('basic'))
@Controller({
  path: 'manual-actions',
  version: '1',
})
export class ManualActionController {
  private readonly logger = new Logger(ManualActionController.name);
  constructor(private readonly manualActionService: ManualActionService) {}
}
