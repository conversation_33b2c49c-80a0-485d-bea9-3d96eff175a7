# Manual Action Module

This module provides API endpoints to manually trigger automated processes that would normally run on a schedule via cron jobs.

## Authentication

All endpoints in this module require Basic Authentication. Set the following environment variables:

```
MANUAL_ACTION_USERNAME=admin
MANUAL_ACTION_PASSWORD=secretpassword
```

Replace `admin` and `secretpassword` with secure credentials.

## Available Endpoints

### Trigger Data Aggregation

Manually triggers the data aggregation process that normally runs at midnight every day.

**Endpoint:** `POST /api/manual-actions/aggregate-data`

**Authentication:** Basic Auth

**Example Request:**

```bash
curl -X POST https://your-api-domain.com/api/manual-actions/aggregate-data \
  -u admin:secretpassword
```

**Example Response:**

```json
{
  "status": "success",
  "timestamp": "2023-04-14T12:34:56.789Z"
}
```

## Error Handling

- **401 Unauthorized**: Invalid or missing credentials
- **500 Internal Server Error**: Error triggering the process

## Security Considerations

- Always use HTTPS for these endpoints
- Use strong, unique credentials
- Consider implementing IP whitelisting
- Monitor all requests to these endpoints 