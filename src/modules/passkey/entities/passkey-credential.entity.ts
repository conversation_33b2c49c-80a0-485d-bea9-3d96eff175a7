import { CustomBaseEntity } from '@app/common/entities/base.entity';
import { Column, Entity } from 'typeorm';

@Entity('passkey_credential')
export class PasskeyCredentialEntity extends CustomBaseEntity {
  @Column({ type: 'varchar', length: 255 })
  credentialId: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  userId: string;

  @Column({ type: 'text', nullable: false })
  publicKey: string;

  @Column({ type: 'int', unsigned: true, nullable: false, default: 0 })
  counter: number;

  @Column({ type: 'json' })
  transports: any[];

  @Column({ type: 'varchar', length: 125, nullable: false })
  origin: string;
}
