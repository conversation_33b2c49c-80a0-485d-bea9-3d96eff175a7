import { HttpErrorException } from '@app/common/exception';
import { LoginResponseDto, TwoFactorLoginResponseDto } from '@app/modules/auth/dto/auth-response.dto';
import { AuthService } from '@app/modules/auth/services/auth.service';
import { UserLoginService } from '@app/modules/auth/services/user-login.service';
import { JwtPayloadType } from '@app/modules/auth/strategies/types/jwt-payload.type';
import {
  PasskeyLoginBeginDto,
  PasskeyLoginBeginInput,
} from '@app/modules/passkey/dto/passkey-login-begin.dto';
import { PasskeyLoginCompleteInput } from '@app/modules/passkey/dto/passkey-login-complete.dto';
import {
  PasskeyRegisterBeginDto,
  PasskeyRegisterBeginInput,
} from '@app/modules/passkey/dto/passkey-register-begin.dto';
import { PasskeyRegisterCompleteInput } from '@app/modules/passkey/dto/passkey-register-complete.dto';
import { PasskeyConfigType } from '@app/modules/passkey/passkey.config';
import { PASSKEY_ERROR_CODES } from '@app/modules/passkey/passkey.error-code';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import * as ms from 'ms';
import { PasskeyService } from './passkey.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

@Injectable()
export class AuthPasskeyService {
  constructor(
    private readonly configService: ConfigService<{
      passkey: PasskeyConfigType;
    }>,
    private readonly passkeyService: PasskeyService,
    private readonly userLoginService: UserLoginService,
    private readonly authService: AuthService,
    private readonly challenges: Map<
      string,
      { challenge: string; expires: number }
    >,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  /**
   * Begins the passkey registration process.
   *
   * @param {JwtPayloadType} jwtPayload - The JWT payload.
   * @param {RegisterPasskeyBeginInput} input - The input.
   * @returns {Promise<{ options: PublicKeyCredentialCreationOptionsJSON; challengeKey: string }>} The passkey registration options and challenge key.
   */
  async registerPasskeyBegin(
    jwtPayload: JwtPayloadType,
    input: PasskeyRegisterBeginInput,
  ): Promise<PasskeyRegisterBeginDto> {
    const origin = input.origin;
    const userLogin = await this.userLoginService.getByUserIdOrThrow(
      jwtPayload.sub,
      ['user'],
    );

    // Generate WebAuthn registration options
    const options = await this.passkeyService.generateRegistrationOptions(
      userLogin.user,
      userLogin,
    );

    // Store challenge temporarily
    const challengeKey = this.createChallengeKey(userLogin.userId, origin);

    await this.storeChallenge(challengeKey, options.challenge);

    return {
      options,
    };
  }

  /**
   * Creates a challenge key.
   *
   * @param {string} userId - The ID of the user.
   * @param {string} origin - The origin.
   * @returns {string} The challenge key.
   */
  private createChallengeKey(userId: string, origin: string): string {
    const challengePrefix = this.configService.getOrThrow<PasskeyConfigType>(
      'passkey.challengePrefix',
      {
        infer: true,
      },
    );
    return `${challengePrefix}:${userId}:${origin}`;
  }

  /**
   * Stores a challenge in the cache.
   *
   * @param {string} challengeKey - The key to store the challenge.
   * @param {string} challenge - The challenge to store.
   * @returns {Promise<void>} The challenge stored.
   */
  private async storeChallenge(
    challengeKey: string,
    challenge: string,
  ): Promise<void> {
    const challengeExpirationTime = this.configService.getOrThrow(
      'passkey.challengeExpirationTime',
      {
        infer: true,
      },
    );
    const challengeExpirationTimeInSeconds = ms(challengeExpirationTime) / 1000;
    await this.cacheManager.set(
      challengeKey,
      challenge,
      challengeExpirationTimeInSeconds,
    );
  }

  /**
   * Cleans up a challenge.
   *
   * @param {string} challengeKey - The key to clean up.
   * @returns {Promise<void>} The challenge cleaned up.
   */
  private async cleanUpChallenge(challengeKey: string): Promise<void> {
    await this.cacheManager.del(challengeKey);
  }

  /**
   * Gets a challenge from the cache.
   *
   * @param {string} challengeKey - The key to get the challenge.
   * @returns {Promise<string | null>} The challenge.
   */
  private getChallenge(challengeKey: string): Promise<string | null> {
    return this.cacheManager.get(challengeKey);
  }

  /**
   * Completes the passkey registration process.
   *
   * @param {JwtPayloadType} jwtPayload - The JWT payload.
   * @param {PasskeyRegisterCompleteInput} input - The input.
   * @returns {Promise<void>} The passkey registration complete.
   */
  async registerPasskeyComplete(
    jwtPayload: JwtPayloadType,
    input: PasskeyRegisterCompleteInput,
  ): Promise<void> {
    const userLogin = await this.userLoginService.getByUserIdOrThrow(
      jwtPayload.sub,
      ['user'],
    );

    const challengeKey = this.createChallengeKey(
      userLogin.userId,
      input.origin,
    );

    const challenge = await this.getChallenge(challengeKey);
    if (!challenge) {
      throw new HttpErrorException(PASSKEY_ERROR_CODES.CHALLENGE_NOT_FOUND);
    }

    // Verify the registration response
    const verified = await this.passkeyService.verifyRegistrationResponse(
      userLogin,
      input.response,
      challenge,
      input.origin,
    );

    // Clean up challenge
    await this.cleanUpChallenge(challengeKey);

    if (!verified) {
      throw new HttpErrorException(
        PASSKEY_ERROR_CODES.REGISTRATION_VERIFICATION_FAILED,
      );
    }
  }

  /**
   * Begins the passkey login process.
   *
   * @param {PasskeyLoginBeginInput} input - The input.
   * @returns {Promise<PasskeyLoginBeginDto>} The passkey login options.
   */
  async loginPasskeyBegin(
    input: PasskeyLoginBeginInput,
  ): Promise<PasskeyLoginBeginDto> {
    const userLogin = await this.userLoginService.getByUsernameOrThrow(
      input.username,
      ['user'],
    );

    const options =
      await this.passkeyService.generateAuthenticationOptions(userLogin);

    const challengeKey = this.createChallengeKey(
      userLogin.userId,
      input.origin,
    );

    await this.storeChallenge(challengeKey, options.challenge);

    return {
      options,
    };
  }

  /**
   * Completes the passkey login process.
   *
   * @param {PasskeyLoginCompleteInput} input - The input.
   * @param {string} userAgent - The user agent.
   * @param {string} ipAddress - The IP address.
   * @returns {Promise<LoginResponseDto | TwoFactorLoginResponseDto>} The login response.
   */
  async loginPasskeyComplete(
    input: PasskeyLoginCompleteInput,
    userAgent: string,
    ipAddress: string,
  ): Promise<LoginResponseDto | TwoFactorLoginResponseDto> {
    const userLogin = await this.userLoginService.getByUsernameOrThrow(
      input.username,
      ['user'],
    );

    const challengeKey = this.createChallengeKey(
      userLogin.userId,
      input.origin,
    );

    const challenge = await this.getChallenge(challengeKey);
    if (!challenge) {
      throw new HttpErrorException(PASSKEY_ERROR_CODES.CHALLENGE_NOT_FOUND);
    }

    const verification = await this.passkeyService.verifyAuthenticationResponse(
      userLogin,
      input.response,
      challenge,
      input.origin,
    );

    await this.cleanUpChallenge(challengeKey);

    if (!verification.verified) {
      throw new HttpErrorException(
        PASSKEY_ERROR_CODES.AUTHENTICATION_VERIFICATION_FAILED,
      );
    }

    return await this.authService.login(userLogin, userAgent, ipAddress);
  }
}
