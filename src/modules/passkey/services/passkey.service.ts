import { UserLoginEntity } from '@app/modules/auth/entities/user-login.entity';
import { PasskeyCredentialEntity } from '@app/modules/passkey/entities/passkey-credential.entity';
import { PasskeyConfigType } from '@app/modules/passkey/passkey.config';
import { UserEntity } from '@app/modules/user/entities/user.entity';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import {
  generateAuthenticationOptions,
  generateRegistrationOptions,
  verifyAuthenticationResponse,
  verifyRegistrationResponse,
  type AuthenticationResponseJSON,
  type GenerateAuthenticationOptionsOpts,
  type GenerateRegistrationOptionsOpts,
  type RegistrationResponseJSON,
  type VerifyAuthenticationResponseOpts,
  type VerifyRegistrationResponseOpts,
} from '@simplewebauthn/server';
import { Repository } from 'typeorm';

@Injectable()
export class PasskeyService {
  private readonly rpName: string;
  private readonly rpId: string;

  constructor(
    private readonly configService: ConfigService<{
      passkey: PasskeyConfigType;
    }>,
    @InjectRepository(PasskeyCredentialEntity)
    private readonly passkeyCredentialRepository: Repository<PasskeyCredentialEntity>,
  ) {
    this.rpName = this.configService.getOrThrow<PasskeyConfigType>(
      'passkey.rpName',
      {
        infer: true,
      },
    );
    this.rpId = this.configService.getOrThrow<PasskeyConfigType>(
      'passkey.rpId',
      {
        infer: true,
      },
    );
  }

  /**
   * Generates WebAuthn registration options for a user.
   *
   * @param {UserEntity} user - The user details.
   * @param {UserLoginEntity} userLogin - The user login details.
   * @returns {Promise<PublicKeyCredentialCreationOptionsJSON>} The WebAuthn registration options.
   */
  async generateRegistrationOptions(
    user: UserEntity,
    userLogin: UserLoginEntity,
  ) {
    const passkeyCredentials = await this.getPasskeyCredentials(user.id);
    // Convert userHandle to Uint8Array as required by SimpleWebAuthn
    const userIdBytes = new TextEncoder().encode(user.id);
    const userDisplayName = user.firstName + ' ' + user.lastName;
    const options: GenerateRegistrationOptionsOpts = {
      rpName: this.rpName,
      rpID: this.rpId,
      userID: userIdBytes,
      userName: userLogin.username,
      userDisplayName,
      attestationType: 'none',
      excludeCredentials: this.mapCredentialsForExclusion(
        passkeyCredentials || [],
      ),
      authenticatorSelection: {
        residentKey: 'preferred',
        userVerification: 'preferred',
      },
    };

    return await generateRegistrationOptions(options);
  }

  /**
   * Verifies a WebAuthn registration response.
   *
   * @param {UserLoginEntity} userLogin - The user login details.
   * @param {RegistrationResponseJSON} response - The WebAuthn registration response.
   * @param {string} expectedChallenge - The expected challenge.
   * @param {string} origin - The origin of the request.
   * @returns {Promise<VerificationResult>} The verification result.
   */
  async verifyRegistrationResponse(
    userLogin: UserLoginEntity,
    response: RegistrationResponseJSON,
    expectedChallenge: string,
    origin: string,
  ) {
    const verification: VerifyRegistrationResponseOpts = {
      response,
      expectedChallenge,
      expectedOrigin: origin,
      expectedRPID: this.rpId,
    };

    const verificationResult = await verifyRegistrationResponse(verification);

    if (verificationResult.verified && verificationResult.registrationInfo) {
      const { credential } = verificationResult.registrationInfo;

      // Save the credential
      await this.passkeyCredentialRepository.save({
        id: credential.id,
        publicKey: Buffer.from(credential.publicKey).toString('base64url'),
        counter: credential.counter,
        userId: userLogin.userId,
        transports: response.response?.transports,
      });
    }

    return verificationResult;
  }

  /**
   * Generates WebAuthn authentication options for a user.
   *
   * @param {UserLoginEntity} userLogin - The user login details.

   * @returns {Promise<PublicKeyCredentialRequestOptionsJSON>} The WebAuthn authentication options.
   */
  async generateAuthenticationOptions(userLogin: UserLoginEntity) {
    const passkeyCredentials = await this.getPasskeyCredentials(
      userLogin.userId,
    );
    const options: GenerateAuthenticationOptionsOpts = {
      rpID: this.rpId,
      allowCredentials: this.mapCredentialsForAuthentication(
        passkeyCredentials || [],
      ),
      userVerification: 'preferred',
    };

    return await generateAuthenticationOptions(options);
  }

  /**
   * Verifies a WebAuthn authentication response.
   *
   * @param {UserLoginEntity} userLogin - The user login details.
   * @param {AuthenticationResponseJSON} response - The WebAuthn authentication response.
   * @param {string} expectedChallenge - The expected challenge.
   * @param {string} origin - The origin of the request.
   * @returns {Promise<VerificationResult>} The verification result.
   */
  async verifyAuthenticationResponse(
    userLogin: UserLoginEntity,
    response: AuthenticationResponseJSON,
    expectedChallenge: string,
    origin: string,
  ) {
    // Find the credential being used
    const credentialId = response.rawId;
    const credential = await this.passkeyCredentialRepository.findOne({
      where: { id: credentialId, userId: userLogin.userId },
    });
    if (!credential) {
      throw new BadRequestException('Credential not found');
    }

    const verification: VerifyAuthenticationResponseOpts = {
      response,
      expectedChallenge,
      expectedOrigin: origin,
      expectedRPID: this.rpId,
      credential: {
        id: credential.id,
        publicKey: Buffer.from(credential.publicKey, 'base64url'),
        counter: Number(credential.counter),
      },
    };

    const verificationResult = await verifyAuthenticationResponse(verification);

    if (verificationResult.verified) {
      /*
       * The counter is a non-decreasing integer value stored on the authenticator and associated with each credential (passkey).
       * It is incremented every time the authenticator is used to sign an authentication assertion (i.e., every login or authentication event).
       * The relying party (the website or service) stores the last seen counter value for each credential. On each authentication, it compares the new counter value to the previous one.
       */
      // Update the counter
      credential.counter = verificationResult.authenticationInfo.newCounter;
      await this.passkeyCredentialRepository.save(credential);
    }

    return verificationResult;
  }

  /**
   * Gets the passkey credentials for a user.
   *
   * @param {string} userId - The ID of the user.
   * @returns {Promise<PasskeyCredentialEntity[]>} The passkey credentials.
   */
  async getPasskeyCredentials(
    userId: string,
  ): Promise<PasskeyCredentialEntity[]> {
    return await this.passkeyCredentialRepository.find({
      where: { userId },
    });
  }

  /**
   * Finds a passkey credential by its ID.
   *
   * @param {string} credentialId - The ID of the passkey credential.
   * @returns {Promise<PasskeyCredentials | null>} The passkey credential.
   */
  async findCredentialById(
    credentialId: string,
  ): Promise<PasskeyCredentialEntity | null> {
    return await this.passkeyCredentialRepository.findOne({
      where: { id: credentialId },
    });
  }

  /**
   * Maps credentials for exclusion.
   *
   * @param {PasskeyCredentialEntity[]} credentials - The passkey credentials.
   * @returns {Array<{id: string, type: 'public-key', transports?: AuthenticatorTransport[]}>} The mapped credentials.
   */
  private mapCredentialsForExclusion(credentials: PasskeyCredentialEntity[]) {
    return credentials.map((credential) => ({
      id: credential.id,
      type: 'public-key' as const,
      transports: credential.transports
        ? (credential.transports as AuthenticatorTransport[])
        : undefined,
    }));
  }

  /**
   * Maps credentials for authentication.
   *
   * @param {PasskeyCredentialEntity[]} credentials - The passkey credentials.
   * @returns {Array<{id: string, type: 'public-key', transports?: AuthenticatorTransport[]}>} The mapped credentials.
   */
  private mapCredentialsForAuthentication(
    credentials: PasskeyCredentialEntity[],
  ): {
    id: string;
    type: 'public-key';
    transports?: AuthenticatorTransport[];
  }[] {
    return credentials.map((credential) => ({
      id: credential.id,
      type: 'public-key' as const,
      transports: credential.transports
        ? (credential.transports as AuthenticatorTransport[])
        : undefined,
    }));
  }
}
