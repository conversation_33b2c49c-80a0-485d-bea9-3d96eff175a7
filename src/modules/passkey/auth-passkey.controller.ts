import { getIpAddress, getUserAgent } from '@app/common/utils/request';
import { User } from '@app/modules/auth/decorators/user.decorator';
import { JwtPayloadType } from '@app/modules/auth/strategies/types/jwt-payload.type';
import {
  PasskeyLoginBeginDto,
  PasskeyLoginBeginInput,
} from '@app/modules/passkey/dto/passkey-login-begin.dto';
import { PasskeyLoginCompleteInput } from '@app/modules/passkey/dto/passkey-login-complete.dto';
import {
  PasskeyRegisterBeginDto,
  PasskeyRegisterBeginInput,
} from '@app/modules/passkey/dto/passkey-register-begin.dto';
import { PasskeyRegisterCompleteInput } from '@app/modules/passkey/dto/passkey-register-complete.dto';
import { AuthPasskeyService } from '@app/modules/passkey/services/auth-passkey.service';
import { Body, Controller, Post, Req } from '@nestjs/common';
import {
  Api<PERSON>earerA<PERSON>,
  ApiBody,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { Request } from 'express';

@Controller({
  path: 'auth/passkey',
  version: '1',
})
export class AuthPasskeyController {
  constructor(private readonly authPasskeyService: AuthPasskeyService) {}

  @ApiOperation({ summary: 'Begin passkey registration' })
  @ApiResponse({
    status: 200,
    description: 'Passkey registration options',
    type: PasskeyRegisterBeginDto,
  })
  @ApiBody({
    type: PasskeyRegisterBeginInput,
  })
  @ApiBearerAuth()
  @Post('begin-registration ')
  async registerBegin(
    @User() jwtPayload: JwtPayloadType,
    @Body() input: PasskeyRegisterBeginInput,
  ) {
    return this.authPasskeyService.registerPasskeyBegin(jwtPayload, input);
  }

  @ApiOperation({ summary: 'Complete passkey registration' })
  @ApiResponse({
    status: 200,
    description: 'Passkey registration complete',
  })
  @ApiBody({
    type: PasskeyRegisterCompleteInput,
  })
  @ApiBearerAuth()
  @Post('complete-registration')
  async registerComplete(
    @User() jwtPayload: JwtPayloadType,
    @Body() input: PasskeyRegisterCompleteInput,
  ) {
    return this.authPasskeyService.registerPasskeyComplete(jwtPayload, input);
  }

  @ApiOperation({ summary: 'Begin passkey login' })
  @ApiResponse({
    status: 200,
    description: 'Passkey login options',
    type: PasskeyLoginBeginDto,
  })
  @ApiBody({
    type: PasskeyLoginBeginInput,
  })
  @Post('begin-login')
  async loginBegin(@Body() input: PasskeyLoginBeginInput) {
    return this.authPasskeyService.loginPasskeyBegin(input);
  }

  @ApiOperation({ summary: 'Complete passkey login' })
  @ApiResponse({
    status: 200,
    description: 'Passkey login complete',
  })
  @ApiBody({
    type: PasskeyLoginCompleteInput,
  })
  @Post('complete-login')
  async loginComplete(
    @User() jwtPayload: JwtPayloadType,
    @Body() input: PasskeyLoginCompleteInput,
    @Req() req: Request,
  ) {
    const userAgent = getUserAgent(req);
    const ipAddress = getIpAddress(req);
    return this.authPasskeyService.loginPasskeyComplete(
      input,
      userAgent,
      ipAddress,
    );
  }
}
