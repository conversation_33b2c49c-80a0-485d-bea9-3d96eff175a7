import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PasskeyCredentialEntity } from './entities/passkey-credential.entity';
import passkeyConfig from './passkey.config';
import { PasskeyService } from './services/passkey.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([PasskeyCredentialEntity]),
    ConfigModule.forRoot({
      load: [passkeyConfig],
    }),
  ],
  providers: [PasskeyService],
  exports: [PasskeyService],
})
export class PasskeyModule {}
