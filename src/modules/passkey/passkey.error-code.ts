import { ErrorCode } from '@app/common/types';
import { HttpStatus } from '@nestjs/common';

const PASSKEY_NAMESPACE = 'PASSKEY';

type PasskeyErrorCodes =
  | 'CHALLENGE_NOT_FOUND'
  | 'REGISTRATION_VERIFICATION_FAILED'
  | 'AUTHENTICATION_VERIFICATION_FAILED';

export const PASSKEY_ERROR_CODES: Record<PasskeyErrorCodes, ErrorCode> = {
  CHALLENGE_NOT_FOUND: {
    code: `${PASSKEY_NAMESPACE}:10000`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Challenge not found',
  },
  REGISTRATION_VERIFICATION_FAILED: {
    code: `${PASSKEY_NAMESPACE}:10001`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Registration verification failed',
  },
  AUTHENTICATION_VERIFICATION_FAILED: {
    code: `${PASSKEY_NAMESPACE}:10002`,
    statusCode: HttpStatus.BAD_REQUEST,
    message: 'Authentication verification failed',
  },
};
