import { ApiProperty } from '@nestjs/swagger';
import { AuthenticationResponseJSON } from '@simplewebauthn/server';
import { IsNotEmpty, IsObject, IsString } from 'class-validator';

export class PasskeyLoginCompleteInput {
  @ApiProperty({
    description: 'The username',
    example: 'john.doe',
  })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({
    description: 'The origin',
    example: 'http://localhost:3000',
  })
  @IsNotEmpty()
  @IsString()
  origin: string;

  @ApiProperty({
    description: 'The response from the passkey',
    example: {
      id: 'TZcGf35Lgn_J4sKWDjrxRPtNAdmc_PJ0Tmg8YEz7JwM',
      rawId: 'TZcGf35Lgn_J4sKWDjrxRPtNAdmc_PJ0Tmg8YEz7JwM',
      response: {
        authenticatorData: 'SZYN5YgOjGh0NBcPZHZgW4_krrmihjLHmVzzuoMdl2MFAAAAAA',
        clientDataJSON:
          'eyJ0eXBlIjoid2ViYXV0aG4uZ2V0IiwiY2hhbGxlbmdlIjoiQ2JwcTFsYUZCS245cXo2cmdwR2J4dVViQ1Y2SzZPSU1nOXFtVExtT1lpayIsIm9yaWdpbiI6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMCIsImNyb3NzT3JpZ2luIjpmYWxzZX0',
        signature:
          'MEUCIBWhu2VLq0U7TlHKfeUXVUDWsNtiULGh98sAtG7chQiIAiEAsmsjpg42J1PZRYXjU6NBtzMScThhA4bijMezUloje6w',
        userHandle:
          'bjRpWW02dWhNZGNTQl9DVmFQX1RTVV9ReHF3eFdiWl9lVk5EOFVncGxXYw',
      },
      type: 'public-key',
      clientExtensionResults: {},
      authenticatorAttachment: 'platform',
    },
  })
  @IsNotEmpty()
  @IsObject()
  response: AuthenticationResponseJSON;
}
