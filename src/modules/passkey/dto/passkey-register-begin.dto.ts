import { ApiProperty } from '@nestjs/swagger';
import { PublicKeyCredentialCreationOptionsJSON } from '@simplewebauthn/server';
import { IsNotEmpty, IsString } from 'class-validator';

export class PasskeyRegisterBeginInput {
  @ApiProperty({
    description: 'The origin',
    example: 'http://localhost:3000',
  })
  @IsNotEmpty()
  @IsString()
  origin: string;
}

export class PasskeyRegisterBeginDto {
  @ApiProperty({
    description: 'The response from the passkey',
    example: {
      challenge: 'Ed8PZCA-19-xdn_YjXWstN_NrLRyxBUFuzDdZsJMKiU',
      rp: {
        name: 'Passkey Auth Demo',
        id: 'localhost',
      },
      user: {
        id: 'bjRpWW02dWhNZGNTQl9DVmFQX1RTVV9ReHF3eFdiWl9lVk5EOFVncGxXYw',
        name: '<EMAIL>',
        displayName: '<PERSON>',
      },
      pubKeyCredParams: [
        {
          alg: -8,
          type: 'public-key',
        },
        {
          alg: -7,
          type: 'public-key',
        },
        {
          alg: -257,
          type: 'public-key',
        },
      ],
      timeout: 60000,
      attestation: 'none',
      excludeCredentials: [],
      authenticatorSelection: {
        residentKey: 'preferred',
        userVerification: 'preferred',
        requireResidentKey: false,
      },
      extensions: {
        credProps: true,
      },
      hints: [],
    },
  })
  options: PublicKeyCredentialCreationOptionsJSON;
}
