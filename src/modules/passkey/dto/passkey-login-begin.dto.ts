import { ApiProperty } from '@nestjs/swagger';
import { PublicKeyCredentialRequestOptionsJSON } from '@simplewebauthn/server';
import { IsString } from 'class-validator';

export class PasskeyLoginBeginInput {
  @ApiProperty({
    description: 'The username',
    example: 'john.doe',
  })
  @IsString()
  username: string;

  @ApiProperty({
    description: 'The origin',
    example: 'http://localhost:3000',
  })
  @IsString()
  origin: string;
}

export class PasskeyLoginBeginDto {
  @ApiProperty({
    description: 'The options for the passkey',
    example: {
      rpId: 'localhost',
      challenge: 'Cbpq1laFBKn9qz6rgpGbxuUbCV6K6OIMg9qmTLmOYik',
      allowCredentials: [
        {
          id: 'TZcGf35Lgn_J4sKWDjrxRPtNAdmc_PJ0Tmg8YEz7JwM',
          type: 'public-key',
          transports: ['internal'],
        },
      ],
      timeout: 60000,
      userVerification: 'preferred',
    },
  })
  options: PublicKeyCredentialRequestOptionsJSON;
}
