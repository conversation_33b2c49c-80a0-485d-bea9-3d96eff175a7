import { Logger } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

@WebSocketGateway({
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
    credentials: true,
  },
  namespace: '/',
})
export class WebsocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer() server: Server;
  private readonly logger = new Logger(WebsocketGateway.name);

  afterInit() {
    this.logger.log('WebSocket Gateway initialized');
  }

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('join-room')
  async handleJoinRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() room: string,
  ) {
    await client.join(room);
    this.logger.log(`Client ${client.id} joined room: ${room}`);
    return { success: true, room };
  }

  @SubscribeMessage('leave-room')
  async handleLeaveRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() room: string,
  ) {
    await client.leave(room);
    this.logger.log(`Client ${client.id} left room: ${room}`);
    return { success: true, room };
  }

  @SubscribeMessage('join-user-room')
  async handleJoinUserRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() userId: string,
  ) {
    const userRoom = `user:${userId}`;
    await client.join(userRoom);
    this.logger.log(`Client ${client.id} joined user room: ${userRoom}`);
    return { success: true, room: userRoom };
  }

  @SubscribeMessage('message')
  handleMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() payload: { room?: string; message: string },
  ) {
    this.logger.log(`Message received from ${client.id}: ${payload.message}`);

    if (payload.room) {
      // Send to specific room
      this.server.to(payload.room).emit('message', {
        from: client.id,
        message: payload.message,
        timestamp: new Date().toISOString(),
      });
      return { success: true };
    }

    // Broadcast to all clients except the sender
    client.broadcast.emit('message', {
      from: client.id,
      message: payload.message,
      timestamp: new Date().toISOString(),
    });

    return { success: true };
  }
}
