# WebSocket Module with Redis Adapter

This module provides a WebSocket server using Socket.IO with Redis adapter for horizontal scaling.

## Features

- Socket.IO server with Redis adapter for vertical/horizontal scaling
- Room-based messaging
- Event broadcasting
- Service for emitting events from anywhere in the application

## Architecture

The implementation consists of:

1. **RedisIoAdapter** - A custom IoAdapter that integrates Socket.IO with Redis for scaling
2. **WebsocketGateway** - Handles Socket.IO event handlers and business logic
3. **WebsocketService** - A service for emitting events from anywhere in the application

The Redis adapter is configured at the application level in `main.ts` rather than in each gateway, following NestJS best practices.

## Usage

### Connecting to WebSocket Server

```typescript
// Client-side example using Socket.IO client
import { io } from 'socket.io-client';

const socket = io('http://localhost:3000');

socket.on('connect', () => {
  console.log('Connected to server');
});

socket.on('disconnect', () => {
  console.log('Disconnected from server');
});
```

### Joining/Leaving Rooms

```typescript
// Join a room
socket.emit('join-room', 'room-name', (response) => {
  console.log('Joined room:', response);
});

// Leave a room
socket.emit('leave-room', 'room-name', (response) => {
  console.log('Left room:', response);
});
```

### Sending/Receiving Messages

```typescript
// Send a message to all clients
socket.emit('message', { message: 'Hello world!' });

// Send a message to a specific room
socket.emit('message', { room: 'room-name', message: 'Hello room!' });

// Listen for messages
socket.on('message', (data) => {
  console.log(`Message from ${data.from}: ${data.message}`);
  console.log(`Timestamp: ${data.timestamp}`);
});
```

### Using the WebsocketService

```typescript
// In any provider/service
import { WebsocketService } from './websocket.service';

@Injectable()
export class YourService {
  constructor(private readonly websocketService: WebsocketService) {}

  someMethod() {
    // Emit to all clients
    this.websocketService.emitToAll('event-name', { data: 'some data' });

    // Emit to a specific room
    this.websocketService.emitToRoom('room-name', 'event-name', { data: 'room data' });

    // Emit to a specific client
    this.websocketService.emitToClient('client-id', 'event-name', { data: 'client data' });
  }
}
```

## Redis Adapter Configuration

The WebSocket server uses the Socket.IO Redis adapter for scaling across multiple server instances. When deployed in a clustered environment, all server instances will be able to communicate with each other through Redis pub/sub.

### Configuration

The Redis connection is configured using the application's Redis configuration from `redis.config.ts`. Make sure your environment variables are set correctly:

```
REDIS_HOST=localhost
REDIS_PORT=6379
```

In production environments with multiple server instances, ensure all instances connect to the same Redis server. 