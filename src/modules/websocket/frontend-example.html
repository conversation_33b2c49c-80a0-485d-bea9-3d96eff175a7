<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WebSocket Notification Demo</title>
  <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 20px;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
    }
    .container {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
    .card {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      border: 1px solid #eee;
    }
    .connection-status {
      padding: 10px;
      border-radius: 4px;
      font-weight: bold;
      text-align: center;
    }
    .connected {
      background-color: #e6f7e6;
      color: #2e7d32;
    }
    .disconnected {
      background-color: #ffebee;
      color: #c62828;
    }
    .notification {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      border-left: 4px solid #2196f3;
      background-color: #e3f2fd;
    }
    .notification.success {
      border-left-color: #4caf50;
      background-color: #e8f5e9;
    }
    .notification.error {
      border-left-color: #f44336;
      background-color: #ffebee;
    }
    .notification pre {
      margin: 10px 0 0;
      padding: 10px;
      background: rgba(0, 0, 0, 0.04);
      border-radius: 4px;
      overflow-x: auto;
    }
    button {
      background: #2196f3;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    button:hover {
      background: #1976d2;
    }
    input {
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      width: 200px;
    }
    .form-group {
      display: flex;
      gap: 10px;
      align-items: center;
      margin-bottom: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>WebSocket Notification Demo</h1>
    
    <div class="card">
      <h2>Connection Status</h2>
      <div id="connectionStatus" class="connection-status disconnected">Disconnected</div>
      <div class="form-group">
        <button id="connectBtn">Connect to WebSocket</button>
        <button id="disconnectBtn" disabled>Disconnect</button>
      </div>
    </div>
    
    <div class="card">
      <h2>User Room</h2>
      <div class="form-group">
        <label for="userId">User ID:</label>
        <input type="text" id="userId" placeholder="Enter user ID">
        <button id="joinUserRoomBtn">Join User Room</button>
      </div>
    </div>
    
    <div class="card">
      <h2>Notifications</h2>
      <button id="clearNotificationsBtn">Clear Notifications</button>
      <div id="notifications"></div>
    </div>
  </div>

  <script>
    let socket;
    const serverUrl = 'http://localhost:3000'; // Change to your backend URL
    
    // DOM Elements
    const connectBtn = document.getElementById('connectBtn');
    const disconnectBtn = document.getElementById('disconnectBtn');
    const joinUserRoomBtn = document.getElementById('joinUserRoomBtn');
    const userIdInput = document.getElementById('userId');
    const connectionStatus = document.getElementById('connectionStatus');
    const notificationsContainer = document.getElementById('notifications');
    const clearNotificationsBtn = document.getElementById('clearNotificationsBtn');
    
    // Connect to WebSocket
    connectBtn.addEventListener('click', () => {
      socket = io(serverUrl);
      
      socket.on('connect', () => {
        updateConnectionStatus(true);
        addNotification('Connected to WebSocket server', 'success');
        console.log('Connected to WebSocket server');
      });
      
      socket.on('disconnect', () => {
        updateConnectionStatus(false);
        addNotification('Disconnected from WebSocket server', 'error');
        console.log('Disconnected from WebSocket server');
      });
      
      socket.on('onboarding:success', (data) => {
        addNotification('Onboarding successful', 'success', data);
        console.log('Onboarding successful:', data);
      });
      
      socket.on('background:complete', (data) => {
        const status = data.status === 'completed' ? 'success' : 'error';
        const message = data.status === 'completed' 
          ? 'Background process completed' 
          : 'Background process failed';
        
        addNotification(message, status, data);
        console.log('Background process notification:', data);
      });
    });
    
    // Disconnect from WebSocket
    disconnectBtn.addEventListener('click', () => {
      if (socket) {
        socket.disconnect();
        socket = null;
      }
    });
    
    // Join user room
    joinUserRoomBtn.addEventListener('click', () => {
      const userId = userIdInput.value.trim();
      if (!userId) {
        alert('Please enter a user ID');
        return;
      }
      
      if (!socket || !socket.connected) {
        alert('Please connect to WebSocket server first');
        return;
      }
      
      socket.emit('join-user-room', userId, (response) => {
        if (response.success) {
          addNotification(`Joined user room: ${response.room}`, 'success');
          console.log('Joined user room:', response);
        } else {
          addNotification('Failed to join user room', 'error', response);
          console.error('Failed to join user room:', response);
        }
      });
    });
    
    // Clear notifications
    clearNotificationsBtn.addEventListener('click', () => {
      notificationsContainer.innerHTML = '';
    });
    
    // Update connection status
    function updateConnectionStatus(isConnected) {
      connectionStatus.textContent = isConnected ? 'Connected' : 'Disconnected';
      connectionStatus.className = `connection-status ${isConnected ? 'connected' : 'disconnected'}`;
      connectBtn.disabled = isConnected;
      disconnectBtn.disabled = !isConnected;
    }
    
    // Add notification
    function addNotification(message, type = 'info', data = null) {
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      
      const messageEl = document.createElement('div');
      messageEl.textContent = message;
      notification.appendChild(messageEl);
      
      if (data) {
        const pre = document.createElement('pre');
        pre.textContent = JSON.stringify(data, null, 2);
        notification.appendChild(pre);
      }
      
      notificationsContainer.prepend(notification);
    }
  </script>
</body>
</html> 