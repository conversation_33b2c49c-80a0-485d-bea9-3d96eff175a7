import { Injectable, Logger } from '@nestjs/common';
import { WebsocketGateway } from './websocket.gateway';

@Injectable()
export class WebsocketService {
  private readonly logger = new Logger(WebsocketService.name);

  constructor(private readonly websocketGateway: WebsocketGateway) {}

  /**
   * Emit an event to all connected clients
   * @param event The event name
   * @param data The data to send
   */
  emitToAll<T>(event: string, data: T): void {
    this.logger.debug(`Emitting event ${event} to all clients`);
    this.websocketGateway.server.emit(event, data);
  }

  /**
   * Emit an event to a specific room
   * @param room The room name
   * @param event The event name
   * @param data The data to send
   */
  emitToRoom<T>(room: string, event: string, data: T): void {
    this.logger.debug(`Emitting event ${event} to room ${room}`);
    this.websocketGateway.server.to(room).emit(event, data);
  }

  /**
   * Emit an event to a specific client
   * @param clientId The client socket ID
   * @param event The event name
   * @param data The data to send
   */
  emitToClient<T>(clientId: string, event: string, data: T): void {
    this.logger.debug(`Emitting event ${event} to client ${clientId}`);
    this.websocketGateway.server.to(clientId).emit(event, data);
  }
}
