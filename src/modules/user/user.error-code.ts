import { ErrorCode } from '@app/common/types';
import { HttpStatus } from '@nestjs/common';

const USER_NAMESPACE = 'USER';

type UserErrorCodes =
  | 'USER_NOT_FOUND'
  | 'USER_ALREADY_EXISTS'
  | 'EMAIL_ALREADY_EXISTS';

export const USER_ERROR_CODES: Record<UserErrorCodes, ErrorCode> = {
  USER_NOT_FOUND: {
    code: `${USER_NAMESPACE}:10000`,
    statusCode: HttpStatus.NOT_FOUND,
    message: 'User not found',
  },
  USER_ALREADY_EXISTS: {
    code: `${USER_NAMESPACE}:10001`,
    statusCode: HttpStatus.CONFLICT,
    message: 'User already exists',
  },
  EMAIL_ALREADY_EXISTS: {
    code: `${USER_NAMESPACE}:10002`,
    statusCode: HttpStatus.CONFLICT,
    message: 'Email already exists',
  },
};
