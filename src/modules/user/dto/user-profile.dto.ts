import { UserRoleEnum } from '@app/modules/user/enums/user-role.enum';
import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class UserProfileDto {
  @ApiProperty({ description: 'The ID of the user' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'The first name of the user' })
  @Expose()
  firstName: string;

  @ApiProperty({ description: 'The last name of the user' })
  @Expose()
  lastName: string;

  @ApiProperty({ description: 'The email of the user' })
  @Expose()
  email: string;

  @ApiProperty({ description: 'The role of the user', enum: UserRoleEnum })
  @Expose()
  role: UserRoleEnum;

  @ApiProperty({ description: 'The location of the user' })
  @Expose()
  location: string;

  @ApiProperty({ description: 'The bio of the user' })
  @Expose()
  bio: string;
}
