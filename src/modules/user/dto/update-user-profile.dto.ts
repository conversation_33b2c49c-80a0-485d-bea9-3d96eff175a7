import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString, MinLength } from 'class-validator';

export class UpdateUserProfileDto {
  @ApiProperty({
    description: 'The first name of the user',
    example: '<PERSON>',
    required: false,
  })
  @Expose()
  @IsString()
  @IsOptional()
  @MinLength(2)
  firstName?: string;

  @ApiProperty({
    description: 'The last name of the user',
    example: '<PERSON><PERSON>',
    required: false,
  })
  @Expose()
  @IsString()
  @IsOptional()
  @MinLength(2)
  lastName?: string;

  @ApiProperty({
    description: 'The location of the user',
    example: 'New York',
    required: false,
  })
  @Expose()
  @IsString()
  @IsOptional()
  @MinLength(2)
  location?: string;

  @ApiProperty({
    description: 'The bio of the user',
    example: 'I am a software engineer',
    required: false,
  })
  @Expose()
  @IsString()
  @IsOptional()
  bio?: string;
}
