import { UserEntity } from '@app/modules/user/entities/user.entity';
import { UserController } from '@app/modules/user/user.controller';
import { UserService } from '@app/modules/user/user.service';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([UserEntity])],
  controllers: [UserController],
  providers: [UserService],
  exports: [UserService],
})
export class UserModule {}
