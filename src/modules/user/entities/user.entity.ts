import { CustomBaseEntity } from '@app/common/entities/base.entity';
import { Nullable } from '@app/common/types';
import { Column, Entity } from 'typeorm';

@Entity('users')
export class UserEntity extends CustomBaseEntity {
  @Column({ name: 'email', unique: true })
  email: string;

  @Column({ name: 'first_name' })
  firstName: string;

  @Column({ name: 'last_name' })
  lastName: string;

  @Column({ name: 'is_email_verified', default: false })
  isEmailVerified: boolean;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'is_two_factor_enabled', default: false })
  isTwoFactorEnabled: boolean;

  @Column({ name: 'two_factor_secret', type: 'varchar', length: 255, nullable: true })
  twoFactorSecret: Nullable<string>;
}
