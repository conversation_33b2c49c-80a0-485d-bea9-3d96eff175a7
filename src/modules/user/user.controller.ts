import { toDto } from '@app/common/dto/to-dto';
import { UserProfileDto } from '@app/modules/user/dto/user-profile.dto';
import { UserService } from '@app/modules/user/user.service';
import { User } from '@modules/auth/decorators/user.decorator';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { JwtPayloadType } from '@modules/auth/strategies/types/jwt-payload.type';
import { Body, Controller, Get, Patch, Post, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserProfileDto } from './dto/update-user-profile.dto';

@ApiTags('Users')
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post('/create')
  register(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  @Get('/profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({
    status: 200,
    description: 'The user profile',
    type: UserProfileDto,
  })
  async getProfile(@User() user: JwtPayloadType) {
    const userProfile = await this.userService.findById(user.sub);
    return toDto(userProfile, UserProfileDto);
  }

  @Patch('/profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({
    status: 200,
    description: 'The updated user profile',
    type: UserProfileDto,
  })
  async updateProfile(
    @User() user: JwtPayloadType,
    @Body() updateUserProfileDto: UpdateUserProfileDto,
  ) {
    const updatedUser = await this.userService.updateProfile(
      user.sub,
      updateUserProfileDto,
    );
    return toDto(updatedUser, UserProfileDto);
  }
}
