import { CdnProvider } from '@app/modules/cdn/cdn.config';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CdnService {
  private readonly logger = new Logger(CdnService.name);
  private readonly enabled: boolean;
  private readonly provider: CdnProvider;
  private readonly domain: string;
  private readonly basePath: string;

  constructor(private readonly configService: ConfigService) {
    this.enabled =
      this.configService.get<boolean>('storage.cdn.enabled') || false;
    this.provider =
      this.configService.get<CdnProvider>('storage.cdn.provider') ||
      CdnProvider.NONE;
    this.domain = this.configService.get<string>('storage.cdn.domain') || '';
    this.basePath =
      this.configService.get<string>('storage.cdn.basePath') || '';

    if (this.enabled && this.provider !== CdnProvider.NONE) {
      this.logger.log(`CDN enabled with provider: ${this.provider}`);
    } else {
      this.logger.log('CDN disabled');
    }
  }

  /**
   * Get the CDN URL for a file
   * @param path - Path to the file
   * @returns CDN URL if enabled, otherwise null
   */
  getCdnUrl(path: string): string | null {
    if (!this.enabled || this.provider === CdnProvider.NONE || !this.domain) {
      return null;
    }

    // Ensure path doesn't start with a slash
    const cleanPath = path.startsWith('/') ? path.substring(1) : path;

    // Combine base path and file path
    const fullPath = this.basePath
      ? `${this.basePath.replace(/\/$/, '')}/${cleanPath}`
      : cleanPath;

    // Build the CDN URL
    return `https://${this.domain}/${fullPath}`;
  }

  /**
   * Transform a URL to use the CDN if enabled
   * @param url - Original URL
   * @returns CDN URL if enabled, otherwise the original URL
   */
  transformUrl(url: string): string {
    if (!this.enabled || this.provider === CdnProvider.NONE || !this.domain) {
      return url;
    }

    // Extract the path from the URL
    try {
      const urlObj = new URL(url);
      const path = urlObj.pathname.startsWith('/')
        ? urlObj.pathname.substring(1)
        : urlObj.pathname;

      // Combine base path and file path
      const fullPath = this.basePath
        ? `${this.basePath.replace(/\/$/, '')}/${path}`
        : path;

      // Build the CDN URL
      return `https://${this.domain}/${fullPath}`;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      // If the URL is invalid, just return the original URL
      this.logger.warn(`Invalid URL: ${url}`);
      return url;
    }
  }

  /**
   * Check if CDN is enabled
   * @returns True if CDN is enabled, otherwise false
   */
  isEnabled(): boolean {
    return this.enabled && this.provider !== CdnProvider.NONE && !!this.domain;
  }

  /**
   * Get the CDN provider
   * @returns CDN provider
   */
  getProvider(): CdnProvider {
    return this.provider;
  }

  /**
   * Get the CDN domain
   * @returns CDN domain
   */
  getDomain(): string {
    return this.domain;
  }
}
