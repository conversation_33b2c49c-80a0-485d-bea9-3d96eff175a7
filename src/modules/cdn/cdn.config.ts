import { registerAs } from '@nestjs/config';
import validateConfig from '@app/common/config/validate-config';
import { IsString, IsOptional } from 'class-validator';

export enum CdnProvider {
  NONE = 'none',
  CLOUDFRONT = 'cloudfront',
  CLOUDFLARE = 'cloudflare',
  CUSTOM = 'custom',
}

export type CdnConfigType = {
  provider: CdnProvider;
  domain?: string;
  basePath: string;
  enabled: boolean;
};

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  CDN_PROVIDER: CdnProvider;

  @IsString()
  @IsOptional()
  CDN_DOMAIN: string;

  @IsString()
  @IsOptional()
  CDN_BASE_PATH: string;

  @IsString()
  @IsOptional()
  CDN_ENABLED: string;
}

export default registerAs<CdnConfigType>('cdn', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);
  return {
    provider: (process.env.CDN_PROVIDER as CdnProvider) || CdnProvider.NONE,
    domain: process.env.CDN_DOMAIN,
    basePath: process.env.CDN_BASE_PATH || '',
    enabled: process.env.CDN_ENABLED === 'true',
  };
});
