import validateConfig from '@common/config/validate-config';
import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import {
  IsBooleanString,
  IsNotEmpty,
  IsOptional,
  IsPort,
  IsString,
} from 'class-validator';
import { config } from 'dotenv';
import * as path from 'path';

config();

class EnvironmentVariablesValidator {
  @IsString()
  @IsNotEmpty()
  DB_HOST: string;

  @IsNotEmpty()
  @IsPort()
  @IsOptional()
  DB_PORT: string;

  @IsString()
  @IsNotEmpty()
  DB_USERNAME: string;

  @IsString()
  @IsNotEmpty()
  DB_PASSWORD: string;

  @IsString()
  @IsNotEmpty()
  DB_NAME: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  DB_SCHEMA: string;

  @IsBooleanString()
  @IsOptional()
  DB_SSL: string;
}

export const databaseConfig = registerAs<TypeOrmModuleOptions>(
  'database',
  () => {
    validateConfig(process.env, EnvironmentVariablesValidator);

    return {
      type: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432', 10),
      username: process.env.DB_USERNAME || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: process.env.DB_NAME || 'postgres',
      entities: [path.join(__dirname, '..', '**', '*.entity{.ts,.js}')],
      synchronize: true,
      migrations: [
        path.join(__dirname, '..', 'migrations', '*.entity{.ts,.js}'),
      ],
      // migrationsRun: true,
      schema: process.env.DB_SCHEMA || 'public',
      logging: true,
      ssl:
        process.env.DB_SSL === 'true'
          ? {
              rejectUnauthorized: false,
            }
          : undefined,
    };
  },
);
