import validateConfig from '@app/common/config/validate-config';
import { registerAs } from '@nestjs/config';
import { IsBooleanString, IsOptional, IsString } from 'class-validator';

export type AlertConfigType = {
  slack?: {
    enabled: boolean;
    channel: string;
    token: string;
  };
  email?: {
    enabled: boolean;
    templateName?: string;
    adminEmail: string;
  };
  telegram?: {
    enabled: boolean;
    token: string;
    chatId: string;
  };
  discord?: {
    enabled: boolean;
    webhookUrl: string;
  };
};

class EnvironmentVariablesValidator {
  @IsBooleanString()
  @IsOptional()
  SLACK_ALERT_ENABLED: string;

  @IsString()
  @IsOptional()
  SLACK_ALERT_CHANNEL: string;

  @IsString()
  @IsOptional()
  SLACK_ALERT_TOKEN: string;

  @IsBooleanString()
  @IsOptional()
  EMAIL_ALERT_ENABLED: string;

  @IsString()
  @IsOptional()
  EMAIL_ALERT_TEMPLATE_NAME: string;

  @IsString()
  @IsOptional()
  EMAIL_ALERT_ADMIN_EMAIL: string;

  @IsBooleanString()
  @IsOptional()
  TELEGRAM_ALERT_ENABLED: string;

  @IsString()
  @IsOptional()
  TELEGRAM_ALERT_TOKEN: string;

  @IsString()
  @IsOptional()
  TELEGRAM_ALERT_CHAT_ID: string;

  @IsBooleanString()
  @IsOptional()
  DISCORD_ALERT_ENABLED: string;

  @IsString()
  @IsOptional()
  DISCORD_ALERT_WEBHOOK_URL: string;
}

export const alertConfig = registerAs<AlertConfigType>('alert', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    slack: process.env.SLACK_ALERT_ENABLED
      ? {
          enabled: process.env.SLACK_ALERT_ENABLED === 'true',
          channel: process.env.SLACK_ALERT_CHANNEL || '',
          token: process.env.SLACK_ALERT_TOKEN || '',
        }
      : undefined,
    email: process.env.EMAIL_ALERT_ENABLED
      ? {
          enabled: process.env.EMAIL_ALERT_ENABLED === 'true',
          templateName: process.env.EMAIL_ALERT_TEMPLATE_NAME,
          adminEmail: process.env.EMAIL_ALERT_ADMIN_EMAIL || '',
        }
      : undefined,
    telegram: process.env.TELEGRAM_ALERT_ENABLED
      ? {
          enabled: process.env.TELEGRAM_ALERT_ENABLED === 'true',
          token: process.env.TELEGRAM_ALERT_TOKEN || '',
          chatId: process.env.TELEGRAM_ALERT_CHAT_ID || '',
        }
      : undefined,
    discord: process.env.DISCORD_ALERT_ENABLED
      ? {
          enabled: process.env.DISCORD_ALERT_ENABLED === 'true',
          webhookUrl: process.env.DISCORD_ALERT_WEBHOOK_URL || '',
        }
      : undefined,
  };
});
