import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { alertConfig, AlertConfigType } from './alert.config';
import { AlertModule } from './alert.module';

/**
 * Example of registering the AlertModule directly with options
 */
@Module({
  imports: [
    AlertModule.register({
      email: {
        enabled: true,
      },
      slack: {
        enabled: true,
        channel: 'alerts',
        token: 'xoxb-your-token',
      },
      telegram: {
        enabled: true,
        token: '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11',
        chatId: '-100123456789',
      },
      discord: {
        enabled: true,
        webhookUrl: 'https://discord.com/api/webhooks/123456789/abcdefg',
      },
    }),
  ],
})
export class AppModuleWithDirectConfig {}

/**
 * Example of registering the AlertModule with async configuration
 * using the ConfigService
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [alertConfig],
    }),
    AlertModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (
        configService: ConfigService<{ alert: AlertConfigType }>,
      ) => {
        const config = configService.get('alert', { infer: true });
        return {
          email: config?.email || { enabled: false },
          slack: config?.slack || { enabled: false, channel: '', token: '' },
          telegram: config?.telegram || {
            enabled: false,
            token: '',
            chatId: '',
          },
          discord: config?.discord || { enabled: false, webhookUrl: '' },
        };
      },
      inject: [ConfigService],
    }),
  ],
})
export class AppModuleWithAsyncConfig {}

/**
 * Usage examples in a service:
 *
 * ```
 * import { Injectable } from '@nestjs/common';
 * import { AlertService } from './alert.service';
 *
 * @Injectable()
 * export class ExampleService {
 *   constructor(private readonly alertService: AlertService) {}
 *
 *   async someMethod() {
 *     try {
 *       // Some logic that might fail
 *     } catch (error) {
 *       // Send an error alert (uses all configured alert channels)
 *       await this.alertService.sendErrorAlert(error, {
 *         user: 'user123',
 *         action: 'someMethod',
 *       });
 *     }
 *
 *     // Send a custom alert message
 *     await this.alertService.sendAlert(
 *       'Important notification',
 *       'System notification',
 *       { type: 'info', timestamp: new Date() }
 *     );
 *   }
 * }
 * ```
 */
