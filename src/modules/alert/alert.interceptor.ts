import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AlertService } from './alert.service';

@Injectable()
export class AlertInterceptor implements NestInterceptor {
  constructor(private readonly alertService: AlertService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url, body, headers, user } = request;

    return next.handle().pipe(
      catchError((error) => {
        // Only send alerts for 5xx errors
        if ((error.status && error.status >= 500) || !error.status) {
          void (async () => {
            await this.alertService.sendErrorAlert(error, {
              timestamp: new Date().toISOString(),
              method,
              url,
              body,
              headers: {
                ...headers,
                authorization: headers.authorization ? '[REDACTED]' : undefined,
              },
              user: user
                ? {
                    id: user.id,
                    email: user.email,
                  }
                : undefined,
            });
          })();
        }
        return throwError(() => error);
      }),
    );
  }
}
