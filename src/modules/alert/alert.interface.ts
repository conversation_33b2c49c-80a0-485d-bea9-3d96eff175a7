import { ModuleMetadata } from '@nestjs/common';

export interface AlertOptions {
  email?: {
    enabled: boolean;
    templateDir?: string;
  };
  slack?: {
    enabled: boolean;
    channel: string;
    token: string;
  };
  telegram?: {
    enabled: boolean;
    token: string;
    chatId: string;
  };
  discord?: {
    enabled: boolean;
    webhookUrl: string;
  };
}

export interface AlertAsyncOptions extends Pick<ModuleMetadata, 'imports'> {
  useFactory: (...args: any[]) => Promise<AlertOptions> | AlertOptions;
  inject?: any[];
}

export interface AlertProvider {
  sendAlert(
    message: string,
    subject: string,
    context?: Record<string, any>,
  ): Promise<void>;
}

export type AlertType = 'email' | 'slack' | 'telegram' | 'discord' | 'all';
