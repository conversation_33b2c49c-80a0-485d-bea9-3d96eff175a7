import { AlertConfigType } from '@app/modules/alert/alert.config';
import { AlertProvider } from '@app/modules/alert/alert.interface';
import { MailerService } from '@app/modules/mailer/mailer.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EmailAlertService implements AlertProvider {
  private readonly logger = new Logger(EmailAlertService.name);

  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService<{
      alert: AlertConfigType;
    }>,
  ) {}

  async sendAlert(
    message: string,
    subject: string,
    context: Record<string, any> = {},
  ): Promise<void> {
    try {
      const emailConfig = this.configService.get('alert.email', {
        infer: true,
      });
      if (!emailConfig || !emailConfig.enabled) {
        this.logger.debug('Email alerts are disabled');
        return;
      }

      // Get admin email from config
      const adminEmail = emailConfig.adminEmail;
      if (!adminEmail) {
        this.logger.warn('Admin email not configured for email alerts');
        return;
      }

      // Merge the message into the context
      const alertContext = {
        ...context,
        message,
        timestamp: new Date().toISOString(),
      };

      // Using a template for the email alert if template is specified
      if (emailConfig.templateName) {
        await this.mailerService.sendMailWithTemplate({
          to: adminEmail,
          subject,
          template: emailConfig.templateName,
          context: alertContext,
        });
      } else {
        // Otherwise send a simple text email
        await this.mailerService.sendMail({
          to: adminEmail,
          subject: subject,
          html: this.renderHtml(message, new Date().toISOString(), context),
        });
      }

      this.logger.debug('Email alert sent');
    } catch (err) {
      this.logger.error('Failed to send email alert', err);
    }
  }

  private renderHtml(
    message: string,
    timestamp: string,
    context: Record<string, any>,
  ): string {
    return `
    <h1>Alert</h1>
    <p>Message: ${message}</p>
    <p>Timestamp: ${timestamp}</p>
    <p>Context: ${JSON.stringify(context, null, 2)}</p>
    `;
  }
}
