import { SlackAlertService } from '@app/modules/alert/slack-alert/slack-alert.service';
import {
  CallHandler,
  ExecutionContext,
  Inject,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable()
export class SlackErrorInterceptor implements NestInterceptor {
  constructor(
    @Inject()
    private readonly slackAlertService: SlackAlertService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url, body, headers, user } = request;

    return next.handle().pipe(
      catchError((error) => {
        // Only send alerts for 5xx errors
        if ((error.status && error.status >= 500) || !error.status) {
          void (async () => {
            await this.slackAlertService.sendErrorAlert(error, {
              timestamp: new Date().toISOString(),
              method,
              url,
              body,
              headers: {
                ...headers,
                authorization: headers.authorization ? '[REDACTED]' : undefined,
              },
              user: user
                ? {
                    id: user.id,
                    email: user.email,
                  }
                : undefined,
            });
          })();
        }
        return throwError(() => error);
      }),
    );
  }
}
