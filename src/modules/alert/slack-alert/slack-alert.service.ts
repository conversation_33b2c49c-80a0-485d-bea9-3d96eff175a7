import { AlertConfigType } from '@app/modules/alert/alert.config';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { KnownBlock, WebClient } from '@slack/web-api';

@Injectable()
export class SlackAlertService {
  private readonly logger = new Logger(SlackAlertService.name);
  private client: WebClient;

  constructor(
    private configService: ConfigService<{
      alert: AlertConfigType;
    }>,
  ) {
    const slackAlertConfig = this.configService.get('alert.slack', {
      infer: true,
    });
    if (slackAlertConfig) {
      this.client = new WebClient(slackAlertConfig.token);
    }
  }

  async sendErrorAlert(
    error: Error,
    context: Record<string, any> = {},
  ): Promise<void> {
    try {
      const slackAlertConfig = this.configService.getOrThrow('alert.slack', {
        infer: true,
      });
      if (!slackAlertConfig.enabled) {
        this.logger.debug('Slack alerts are disabled');
        return;
      }

      if (!slackAlertConfig.channel) {
        this.logger.error('Slack channel not configured');
        return;
      }

      const blocks: KnownBlock[] = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '🚨 Error Alert',
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Error:*\n>${error.message}`,
            },
          ],
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Stack:*\n\`\`\`${error.stack}\`\`\``,
          },
        },
      ];

      // Add context if provided
      if (Object.keys(context).length > 0) {
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Additional Context:*\n\`\`\`${JSON.stringify(context, null, 2)}\`\`\``,
          },
        });
      }

      await this.client.chat.postMessage({
        channel: slackAlertConfig.channel,
        blocks,
        text: '🚨 Error Alert',
      });
      this.logger.debug('Slack alert sent');
    } catch (err) {
      this.logger.error('Failed to send Slack alert', err);
    }
  }
}
