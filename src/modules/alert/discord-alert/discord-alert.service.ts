import { AlertConfigType } from '@app/modules/alert/alert.config';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

interface DiscordEmbed {
  title: string;
  description: string;
  color: number;
  timestamp: string;
  fields: Array<{
    name: string;
    value: string;
  }>;
}

@Injectable()
export class DiscordAlertService {
  private readonly logger = new Logger(DiscordAlertService.name);
  private readonly webhookUrl: string;
  private readonly enabled: boolean;

  constructor(
    private configService: ConfigService<{
      alert: AlertConfigType;
    }>,
  ) {
    const discordConfig = this.configService.get('alert.discord', {
      infer: true,
    });

    this.enabled = discordConfig?.enabled || false;
    this.webhookUrl = discordConfig?.webhookUrl || '';

    if (this.enabled && !this.webhookUrl) {
      this.logger.warn('Discord alerts are enabled but webhookUrl is missing');
    }
  }

  async sendAlert(
    message: string,
    subject: string,
    context: Record<string, any> = {},
  ): Promise<void> {
    try {
      const discordConfig = this.configService.get('alert.discord', {
        infer: true,
      });

      if (!discordConfig?.enabled) {
        this.logger.debug('Discord alerts are disabled');
        return;
      }

      if (!discordConfig.webhookUrl) {
        this.logger.error('Discord webhook URL not configured');
        return;
      }

      // Format the Discord embed message
      const embed: DiscordEmbed = {
        title: subject,
        description: message,
        color: 0xff0000, // Red color for alerts
        timestamp: new Date().toISOString(),
        fields: [],
      };

      // Add context as fields if provided
      if (Object.keys(context).length > 0) {
        for (const [key, value] of Object.entries(context)) {
          if (key === 'stack') {
            embed.fields.push({
              name: 'Stack Trace',
              value: `\`\`\`\n${value}\n\`\`\``.substring(0, 1024), // Discord field value has 1024 char limit
            });
          } else {
            const stringValue =
              typeof value === 'object'
                ? JSON.stringify(value, null, 2)
                : String(value);

            embed.fields.push({
              name: key,
              value: `\`\`\`\n${stringValue}\n\`\`\``.substring(0, 1024),
            });
          }
        }
      }

      // Send the message using Discord Webhook API
      await axios.post(discordConfig.webhookUrl, {
        embeds: [embed],
      });

      this.logger.debug('Discord alert sent');
    } catch (err) {
      this.logger.error('Failed to send Discord alert', err);
    }
  }
}
