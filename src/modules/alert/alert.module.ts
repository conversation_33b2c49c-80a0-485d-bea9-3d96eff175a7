import { DynamicModule, Module, Provider } from '@nestjs/common';
import { AlertAsyncOptions, AlertOptions } from './alert.interface';
import { AlertService } from './alert.service';
import { DiscordAlertModule } from './discord-alert/discord-alert.module';
import { EmailAlertModule } from './email-alert/email-alert.module';
import { SlackAlertModule } from './slack-alert/slack-alert.module';
import { TelegramAlertModule } from './telegram-alert/telegram-alert.module';

@Module({})
export class AlertModule {
  static register(options: AlertOptions): DynamicModule {
    return {
      module: AlertModule,
      imports: this.getImports(options),
      providers: [
        {
          provide: 'ALERT_OPTIONS',
          useValue: options,
        },
        AlertService,
      ],
      exports: [AlertService],
    };
  }

  static registerAsync(options: AlertAsyncOptions): DynamicModule {
    // Import all alert modules for registerAsync since we can't determine
    // which ones are enabled until the async factory runs
    const allAlertModules = [
      SlackAlertModule,
      EmailAlertModule,
      TelegramAlertModule,
      DiscordAlertModule,
    ];

    return {
      module: AlertModule,
      imports: [...(options.imports || []), ...allAlertModules],
      providers: [...this.createAsyncProviders(options), AlertService],
      exports: [AlertService],
    };
  }

  private static createAsyncProviders(options: AlertAsyncOptions): Provider[] {
    return [
      {
        provide: 'ALERT_OPTIONS',
        useFactory: options.useFactory,
        inject: options.inject || [],
      },
    ];
  }

  /**
   * Get imports based on enabled options (used for register() method)
   * For registerAsync(), all modules are imported since we can't determine
   * enabled status until the async factory runs.
   */
  private static getImports(options: AlertOptions): Array<any> {
    const imports: Array<any> = [];

    if (options.slack?.enabled) {
      imports.push(SlackAlertModule);
    }

    if (options.email?.enabled) {
      imports.push(EmailAlertModule);
    }

    if (options.telegram?.enabled) {
      imports.push(TelegramAlertModule);
    }

    if (options.discord?.enabled) {
      imports.push(DiscordAlertModule);
    }

    return imports;
  }
}
