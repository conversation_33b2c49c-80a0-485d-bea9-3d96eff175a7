import { AlertConfigType } from '@app/modules/alert/alert.config';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as TelegramBot from 'node-telegram-bot-api';

@Injectable()
export class TelegramAlertService {
  private readonly logger = new Logger(TelegramAlertService.name);
  private readonly apiBaseUrl: string;
  private readonly token: string;
  private readonly chatId: string;
  private readonly enabled: boolean;
  private readonly bot: TelegramBot;

  constructor(
    private configService: ConfigService<{
      alert: AlertConfigType;
    }>,
  ) {
    const telegramConfig = this.configService.get('alert.telegram', {
      infer: true,
    });

    this.enabled = telegramConfig?.enabled || false;
    this.token = telegramConfig?.token || '';
    this.chatId = telegramConfig?.chatId || '';
    this.apiBaseUrl = `https://api.telegram.org/bot${this.token}`;
    this.bot = new TelegramBot(this.token, { polling: false });
    if (this.enabled && (!this.token || !this.chatId)) {
      this.logger.warn(
        'Telegram alerts are enabled but token or chatId is missing',
      );
    }
  }

  async sendAlert(
    message: string,
    subject: string,
    context: Record<string, any> = {},
  ): Promise<void> {
    try {
      const telegramConfig = this.configService.get('alert.telegram', {
        infer: true,
      });

      if (!telegramConfig?.enabled) {
        this.logger.debug('Telegram alerts are disabled');
        return;
      }

      if (!telegramConfig.token || !telegramConfig.chatId) {
        this.logger.error('Telegram token or chatId not configured');
        return;
      }

      // Format the message
      let formattedMessage = `*${subject}*\n\n${message}`;

      // Add context if provided
      if (Object.keys(context).length > 0) {
        const contextString = JSON.stringify(context, null, 2);
        formattedMessage += `\n\n*Additional Context:*\n\`\`\`\n${contextString}\n\`\`\``;
      }

      // Send the message using Telegram API
      await axios.post(`${this.apiBaseUrl}/sendMessage`, {
        chat_id: telegramConfig.chatId,
        text: formattedMessage,
        parse_mode: 'Markdown',
      });

      // await this.bot.sendMessage(telegramConfig.chatId, formattedMessage, {
      //   parse_mode: 'Markdown',
      // });

      this.logger.debug('Telegram alert sent');
    } catch (err) {
      console.log(err);
      this.logger.error('Failed to send Telegram alert');
    }
  }
}
