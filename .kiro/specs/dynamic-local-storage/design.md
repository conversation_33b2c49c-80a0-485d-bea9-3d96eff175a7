# Design Document: Dynamic Local Storage Module

## Overview

This design document outlines the approach for transforming the existing LocalStorageModule into a dynamic module that supports flexible configuration options. The enhanced module will allow developers to pass configuration options directly when importing the module, load configuration asynchronously, and maintain backward compatibility with the existing implementation.

## Architecture

The architecture will follow NestJS's dynamic module pattern, which allows modules to be configured at runtime. The LocalStorageModule will expose static methods (`forRoot` and `forRootAsync`) that return a dynamic module configuration. This approach maintains the existing functionality while adding new capabilities.

### Key Components

1. **LocalStorageModuleOptions Interface**: Defines the configuration options that can be passed to the module.
2. **LocalStorageOptionsFactory Interface**: Defines the contract for classes that can provide configuration options asynchronously.
3. **LocalStorageAsyncOptions Interface**: Defines the structure for asynchronous configuration options.
4. **LocalStorageModule**: The enhanced module that provides static methods for configuration.

## Components and Interfaces

### LocalStorageModuleOptions Interface

```typescript
export interface LocalStorageModuleOptions {
  /**
   * Directory where files will be uploaded
   * @default 'uploads'
   */
  uploadDir?: string;
  
  /**
   * Base URL for generating public URLs
   * @default 'http://localhost:3000'
   */
  baseUrl?: string;
  
  /**
   * Whether to register the module as global
   * @default false
   */
  isGlobal?: boolean;
}
```

### LocalStorageOptionsFactory Interface

```typescript
export interface LocalStorageOptionsFactory {
  createLocalStorageOptions(): 
    Promise<LocalStorageModuleOptions> | 
    LocalStorageModuleOptions;
}
```

### LocalStorageAsyncOptions Interface

```typescript
export interface LocalStorageAsyncOptions {
  /**
   * Injection token
   */
  inject?: any[];
  
  /**
   * Whether to register the module as global
   * @default false
   */
  isGlobal?: boolean;
  
  /**
   * Factory function to create options
   */
  useFactory?: (
    ...args: any[]
  ) => Promise<LocalStorageModuleOptions> | LocalStorageModuleOptions;
  
  /**
   * Class to use for creating options
   */
  useClass?: Type<LocalStorageOptionsFactory>;
  
  /**
   * Existing provider to use for options
   */
  useExisting?: Type<LocalStorageOptionsFactory>;
}
```

### LocalStorageModule

The enhanced LocalStorageModule will provide the following static methods:

1. **forRoot**: Accepts direct configuration options
2. **forRootAsync**: Accepts configuration providers for asynchronous configuration
3. **Default import**: Maintains backward compatibility

## Data Models

### Configuration Provider Token

```typescript
export const LOCAL_STORAGE_OPTIONS = 'LOCAL_STORAGE_OPTIONS';
```

This token will be used to inject the configuration options into the LocalStorageService.

## Error Handling

1. **Configuration Validation**: The module will validate configuration options and throw clear error messages for invalid configurations.
2. **Missing Required Options**: If required options are missing, the module will fall back to environment variables or default values.
3. **Invalid Async Configuration**: If an invalid async configuration is provided (e.g., neither useFactory, useClass, nor useExisting is specified), an error will be thrown during module initialization.

## Testing Strategy

1. **Unit Tests**:
   - Test the forRoot method with various configuration options
   - Test the forRootAsync method with different provider configurations
   - Test backward compatibility with the default import

2. **Integration Tests**:
   - Test the module in a NestJS application with different configuration approaches
   - Test multiple instances of the module with different configurations
   - Test the module's behavior when configuration changes

## Implementation Approach

### 1. Create Module Options Interfaces

Define the interfaces for module options and async configuration.

### 2. Enhance LocalStorageModule

Modify the LocalStorageModule to support dynamic configuration:

```typescript
@Module({})
export class LocalStorageModule {
  static forRoot(options?: LocalStorageModuleOptions): DynamicModule {
    // Implementation
  }
  
  static forRootAsync(options: LocalStorageAsyncOptions): DynamicModule {
    // Implementation
  }
}
```

### 3. Update LocalStorageService

Modify the LocalStorageService to accept injected configuration:

```typescript
@Injectable()
export class LocalStorageService {
  constructor(
    @Inject(LOCAL_STORAGE_OPTIONS)
    private readonly options: LocalStorageModuleOptions,
    private readonly configService: ConfigService,
  ) {
    // Initialize with injected options or fall back to ConfigService
  }
}
```

### 4. Maintain Backward Compatibility

Ensure the module can still be imported without using forRoot or forRootAsync by providing default configuration providers.

## Design Decisions and Rationales

1. **Using NestJS Dynamic Module Pattern**: This approach aligns with NestJS best practices and provides a familiar API for developers who use other NestJS dynamic modules.

2. **Supporting Multiple Configuration Methods**: By supporting both direct configuration (forRoot) and asynchronous configuration (forRootAsync), we provide flexibility for different use cases.

3. **Maintaining Backward Compatibility**: By preserving the existing configuration approach as a fallback, we ensure that existing code continues to work without changes.

4. **Injection Token for Configuration**: Using a dedicated injection token for configuration options allows for clear separation between the configuration and the service implementation.

5. **Supporting Multiple Module Instances**: By properly scoping the configuration, we allow for multiple instances of the module with different configurations, which is useful for managing different storage locations.

## Diagrams

### Module Configuration Flow

```mermaid
flowchart TD
    A[Import LocalStorageModule] --> B{Configuration Method}
    B -->|forRoot| C[Direct Configuration]
    B -->|forRootAsync| D[Async Configuration]
    B -->|Default| E[Environment Variables]
    
    C --> F[Create Module with Options]
    D --> G{Provider Type}
    G -->|useFactory| H[Factory Function]
    G -->|useClass| I[Class Provider]
    G -->|useExisting| J[Existing Provider]
    H --> F
    I --> F
    J --> F
    E --> F
    
    F --> K[Register Providers]
    K --> L[Export LocalStorageService]
```

### Service Initialization Flow

```mermaid
flowchart TD
    A[LocalStorageService Constructor] --> B{Injected Options?}
    B -->|Yes| C[Use Injected Options]
    B -->|No| D[Use ConfigService]
    C --> E[Initialize Service]
    D --> E
```