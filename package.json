{"name": "boilerplate", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "organize-imports": "organize-imports-cli src/**/*.ts", "dev:local": "env-cmd -f .env.local nest start --watch", "dev:staging": "env-cmd -f .env.staging nest start --debug --watch", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli", "migration:create": "npm run typeorm migration:create", "typeorm:dev:local": "env-cmd -f .env.local ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli", "migration:dev:local:run": "npm run typeorm:dev:local migration:run -- -d ./src/ormconfig.ts", "migration:dev:local:generate": "npm run typeorm:dev:local -- -d ./src/ormconfig.ts migration:generate", "migration:dev:local:revert": "npm run typeorm:dev:local -- -d ./src/ormconfig.ts migration:revert", "typeorm:dev:staging": "env-cmd -f .env.staging ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli", "migration:dev:staging:run": "npm run typeorm:dev:staging migration:run -- -d ./src/ormconfig.ts", "migration:dev:staging:generate": "npm run typeorm:dev:staging -- -d ./src/ormconfig.ts migration:generate", "migration:dev:staging:revert": "npm run typeorm:dev:staging -- -d ./src/ormconfig.ts migration:revert", "typeorm:prod": "./node_modules/.bin/typeorm", "migration:prod:generate": "./node_modules/.bin/typeorm migration:generate -d ./dist/ormconfig.js", "migration:prod:revert": "./node_modules/.bin/typeorm migration:revert -d ./dist/ormconfig.js", "migration:prod:run": "./node_modules/.bin/typeorm migration:run -d ./dist/ormconfig.js", "seed:dev:local": "env-cmd -f .env.local ts-node -r tsconfig-paths/register src/seeds/seed.ts", "seed:dev:staging": "env-cmd -f .env.staging ts-node -r tsconfig-paths/register src/seeds/seed.ts", "seed:prod": "node dist/seeds/seed.js", "script": "ts-node -r tsconfig-paths/register script.ts"}, "_moduleAliases": {"@app": "dist", "@common": "dist/common", "@config": "dist/config", "@modules": "dist/modules", "@utils": "dist/utils"}, "dependencies": {"@aws-sdk/client-s3": "^3.846.0", "@aws-sdk/s3-presigned-post": "^3.846.0", "@aws-sdk/s3-request-presigner": "^3.846.0", "@keyv/redis": "^4.4.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.2", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.2", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.2", "@nestjs/platform-socket.io": "^11.1.2", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.2", "@sendgrid/mail": "^8.1.5", "@simplewebauthn/server": "^13.1.1", "@slack/web-api": "^7.9.2", "@socket.io/redis-adapter": "^8.3.0", "bcrypt": "^6.0.0", "cache-manager": "^6.4.3", "cacheable": "^1.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cloudinary": "^2.7.0", "express-http-context": "^2.0.1", "express-session": "^1.18.1", "form-data": "^4.0.3", "handlebars": "^4.7.8", "keyv": "^5.3.3", "mailgun.js": "^12.0.3", "module-alias": "^2.2.3", "node-telegram-bot-api": "^0.66.0", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-http": "^0.3.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-microsoft": "^2.1.0", "pg": "^8.16.0", "redis": "^4.6.13", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "typeorm": "^0.3.24"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.2", "@swc/cli": "^0.7.7", "@swc/core": "^1.11.29", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.2", "@types/express-session": "^1.18.1", "@types/jest": "^29.5.14", "@types/mailgun-js": "^0.22.18", "@types/ms": "^2.1.0", "@types/node": "^22.15.23", "@types/node-telegram-bot-api": "^0.64.9", "@types/nodemailer": "^6.4.17", "@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-http": "^0.3.11", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/passport-microsoft": "^1.0.3", "@types/supertest": "^6.0.3", "env-cmd": "^10.1.0", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "globals": "^16.2.0", "jest": "^29.7.0", "organize-imports-cli": "^0.10.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}